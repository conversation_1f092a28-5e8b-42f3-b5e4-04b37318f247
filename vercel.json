{"name": "cube1-group", "version": 2, "framework": "nextjs", "buildCommand": "pnpm run build", "outputDirectory": ".next", "installCommand": "pnpm install", "devCommand": "pnpm run dev", "env": {"NODE_ENV": "production", "NEXT_PUBLIC_ENV": "production"}, "build": {"env": {"NODE_ENV": "production"}}, "functions": {"app/api/**/*.ts": {"maxDuration": 30}}, "regions": ["hkg1", "sin1"], "github": {"silent": true}}