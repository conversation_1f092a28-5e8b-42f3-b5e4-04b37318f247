/**
 * 数据迁移API - /api/migration
 * 🎯 核心功能：从LocalStorage迁移数据到数据库
 * 🔄 安全迁移：备份、验证、回滚机制
 * ⚡ 渐进式：支持部分迁移和增量更新
 */

import { NextRequest } from 'next/server';
import {
  successResponse,
  errorResponse,
  validationErrorResponse,
  parseRequestBody,
  validateRequired,
  withErrorHandling,
  handleDatabaseError,
} from '@/lib/api-utils';
import { migrateFromLocalStorage, needsMigration } from '@/lib/migration';
import { MigrationRequest, MigrationResponse } from '@/types/api';

// GET /api/migration - 检查是否需要迁移
export const GET = withErrorHandling(async (request: NextRequest) => {
  try {
    const needs = needsMigration();
    
    return successResponse({
      needsMigration: needs,
      message: needs ? '检测到LocalStorage数据，建议进行迁移' : '无需迁移',
    });
  } catch (error) {
    return handleDatabaseError(error);
  }
});

// POST /api/migration - 执行数据迁移
export const POST = withErrorHandling(async (request: NextRequest) => {
  const body = await parseRequestBody<MigrationRequest>(request);
  
  if (!body) {
    return errorResponse('无效的请求体');
  }

  // 验证必需字段
  const requiredErrors = validateRequired(body, ['userId']);
  if (requiredErrors.length > 0) {
    return validationErrorResponse(requiredErrors);
  }

  try {
    console.log('🚀 开始数据迁移，用户ID:', body.userId);
    
    // 执行迁移
    const migrationStatus = await migrateFromLocalStorage(body.userId);
    
    const response: MigrationResponse = {
      isCompleted: migrationStatus.isCompleted,
      lastMigrationDate: migrationStatus.lastMigrationDate?.toISOString(),
      migratedStores: migrationStatus.migratedStores,
      errors: migrationStatus.errors,
    };

    if (migrationStatus.isCompleted) {
      return successResponse(response, '数据迁移成功完成');
    } else {
      return errorResponse('数据迁移失败', 500);
    }
  } catch (error) {
    console.error('迁移API错误:', error);
    return handleDatabaseError(error);
  }
});
