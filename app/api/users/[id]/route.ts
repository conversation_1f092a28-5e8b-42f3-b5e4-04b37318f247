/**
 * 单个用户API - /api/users/[id]
 * 🎯 核心功能：获取、更新、删除特定用户
 * 🔄 RESTful设计：GET, PUT, DELETE
 * ⚡ 类型安全：完整的TypeScript支持
 */

import { NextRequest } from 'next/server';
import prisma from '@/lib/prisma';
import {
  successResponse,
  errorResponse,
  validationErrorResponse,
  notFoundResponse,
  parseRequestBody,
  validateStringLength,
  withErrorHandling,
  handleDatabaseError,
} from '@/lib/api-utils';
import { CreateUserRequest, UserResponse } from '@/types/api';

// GET /api/users/[id] - 获取特定用户
export const GET = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  const { id } = params;

  if (!id) {
    return errorResponse('用户ID是必需的');
  }

  try {
    const user = await prisma.user.findUnique({
      where: { id },
      include: {
        projects: {
          select: {
            id: true,
            name: true,
            description: true,
            createdAt: true,
            updatedAt: true,
          },
        },
      },
    });

    if (!user) {
      return notFoundResponse('用户');
    }

    const userResponse: UserResponse & { projects?: any[] } = {
      id: user.id,
      email: user.email || undefined,
      name: user.name || undefined,
      createdAt: user.createdAt.toISOString(),
      updatedAt: user.updatedAt.toISOString(),
      projects: user.projects.map(project => ({
        id: project.id,
        name: project.name,
        description: project.description,
        createdAt: project.createdAt.toISOString(),
        updatedAt: project.updatedAt.toISOString(),
      })),
    };

    return successResponse(userResponse);
  } catch (error) {
    return handleDatabaseError(error);
  }
});

// PUT /api/users/[id] - 更新用户
export const PUT = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  const { id } = params;

  if (!id) {
    return errorResponse('用户ID是必需的');
  }

  const body = await parseRequestBody<Partial<CreateUserRequest>>(request);
  
  if (!body) {
    return errorResponse('无效的请求体');
  }

  // 验证数据
  const errors: string[] = [];
  
  if (body.email !== undefined) {
    if (body.email) {
      errors.push(...validateStringLength(body.email, 'email', 1, 255));
      
      // 简单的邮箱格式验证
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(body.email)) {
        errors.push('邮箱格式无效');
      }
    }
  }
  
  if (body.name !== undefined && body.name) {
    errors.push(...validateStringLength(body.name, 'name', 1, 100));
  }

  if (errors.length > 0) {
    return validationErrorResponse(errors);
  }

  try {
    // 检查用户是否存在
    const existingUser = await prisma.user.findUnique({
      where: { id },
    });

    if (!existingUser) {
      return notFoundResponse('用户');
    }

    // 检查邮箱是否被其他用户使用
    if (body.email && body.email !== existingUser.email) {
      const emailUser = await prisma.user.findUnique({
        where: { email: body.email },
      });

      if (emailUser) {
        return errorResponse('邮箱已被其他用户使用', 409);
      }
    }

    // 更新用户
    const user = await prisma.user.update({
      where: { id },
      data: {
        email: body.email,
        name: body.name,
      },
    });

    const userResponse: UserResponse = {
      id: user.id,
      email: user.email || undefined,
      name: user.name || undefined,
      createdAt: user.createdAt.toISOString(),
      updatedAt: user.updatedAt.toISOString(),
    };

    return successResponse(userResponse, '用户更新成功');
  } catch (error) {
    return handleDatabaseError(error);
  }
});

// DELETE /api/users/[id] - 删除用户
export const DELETE = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: { id: string } }
) => {
  const { id } = params;

  if (!id) {
    return errorResponse('用户ID是必需的');
  }

  try {
    // 检查用户是否存在
    const existingUser = await prisma.user.findUnique({
      where: { id },
    });

    if (!existingUser) {
      return notFoundResponse('用户');
    }

    // 删除用户（级联删除相关项目）
    await prisma.user.delete({
      where: { id },
    });

    return successResponse(null, '用户删除成功');
  } catch (error) {
    return handleDatabaseError(error);
  }
});
