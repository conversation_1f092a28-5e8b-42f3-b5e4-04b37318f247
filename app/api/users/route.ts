/**
 * 用户管理API - /api/users
 * 🎯 核心功能：用户创建、查询、更新
 * 🔄 RESTful设计：GET, POST, PUT, DELETE
 * ⚡ 类型安全：完整的TypeScript支持
 */

import { NextRequest } from 'next/server';
import prisma from '@/lib/prisma';
import {
  successResponse,
  errorResponse,
  validationErrorResponse,
  notFoundResponse,
  parseRequestBody,
  validateRequired,
  validateStringLength,
  withErrorHandling,
  handleDatabaseError,
} from '@/lib/api-utils';
import { CreateUserRequest, UserResponse } from '@/types/api';

// GET /api/users - 获取所有用户
export const GET = withErrorHandling(async (request: NextRequest) => {
  try {
    const users = await prisma.user.findMany({
      orderBy: { createdAt: 'desc' },
    });

    const userResponses: UserResponse[] = users.map(user => ({
      id: user.id,
      email: user.email || undefined,
      name: user.name || undefined,
      createdAt: user.createdAt.toISOString(),
      updatedAt: user.updatedAt.toISOString(),
    }));

    return successResponse(userResponses);
  } catch (error) {
    return handleDatabaseError(error);
  }
});

// POST /api/users - 创建新用户
export const POST = withErrorHandling(async (request: NextRequest) => {
  const body = await parseRequestBody<CreateUserRequest>(request);
  
  if (!body) {
    return errorResponse('无效的请求体');
  }

  // 验证数据
  const errors: string[] = [];
  
  if (body.email) {
    errors.push(...validateStringLength(body.email, 'email', 1, 255));
    
    // 简单的邮箱格式验证
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(body.email)) {
      errors.push('邮箱格式无效');
    }
  }
  
  if (body.name) {
    errors.push(...validateStringLength(body.name, 'name', 1, 100));
  }

  if (errors.length > 0) {
    return validationErrorResponse(errors);
  }

  try {
    // 检查邮箱是否已存在
    if (body.email) {
      const existingUser = await prisma.user.findUnique({
        where: { email: body.email },
      });

      if (existingUser) {
        return errorResponse('邮箱已被使用', 409);
      }
    }

    // 创建用户
    const user = await prisma.user.create({
      data: {
        email: body.email,
        name: body.name,
      },
    });

    const userResponse: UserResponse = {
      id: user.id,
      email: user.email || undefined,
      name: user.name || undefined,
      createdAt: user.createdAt.toISOString(),
      updatedAt: user.updatedAt.toISOString(),
    };

    return successResponse(userResponse, '用户创建成功');
  } catch (error) {
    return handleDatabaseError(error);
  }
});
