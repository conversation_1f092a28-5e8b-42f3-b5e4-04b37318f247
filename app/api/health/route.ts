/**
 * 健康检查API - /api/health
 * 🎯 核心功能：检查API和数据库连接状态
 * 🔄 监控支持：提供系统健康状态信息
 * ⚡ 快速响应：轻量级检查，快速返回
 */

import { NextRequest } from 'next/server';
import { successResponse, errorResponse, withErrorHandling } from '@/lib/api-utils';
import { healthCheck } from '@/lib/prisma';

// GET /api/health - 健康检查
export const GET = withErrorHandling(async (request: NextRequest) => {
  try {
    // 检查数据库连接
    const dbHealth = await healthCheck();
    
    const healthData = {
      status: dbHealth.status === 'healthy' ? 'ok' : 'error',
      timestamp: new Date().toISOString(),
      database: dbHealth.status,
      api: 'ok',
      version: '1.0.0',
    };
    
    if (dbHealth.status === 'healthy') {
      return successResponse(healthData);
    } else {
      return errorResponse('数据库连接异常', 503);
    }
  } catch (error) {
    return errorResponse('健康检查失败', 503);
  }
});
