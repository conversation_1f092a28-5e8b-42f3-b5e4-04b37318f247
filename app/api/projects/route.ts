/**
 * 项目管理API - /api/projects
 * 🎯 核心功能：项目创建、查询
 * 🔄 RESTful设计：GET, POST
 * ⚡ 类型安全：完整的TypeScript支持
 */

import { NextRequest } from 'next/server';
import prisma from '@/lib/prisma';
import {
  successResponse,
  errorResponse,
  validationErrorResponse,
  parseRequestBody,
  getQueryParams,
  validateRequired,
  validateStringLength,
  withErrorHandling,
  handleDatabaseError,
  parsePaginationParams,
  calculatePagination,
} from '@/lib/api-utils';
import { CreateProjectRequest, ProjectResponse } from '@/types/api';

// GET /api/projects - 获取项目列表
export const GET = withErrorHandling(async (request: NextRequest) => {
  const searchParams = getQueryParams(request);
  const { page, limit } = parsePaginationParams(searchParams);
  const userId = searchParams.get('userId');

  try {
    // 构建查询条件
    const where = userId ? { userId } : {};

    // 获取总数
    const total = await prisma.project.count({ where });

    // 获取分页数据
    const pagination = calculatePagination(page, limit, total);
    
    const projects = await prisma.project.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        _count: {
          select: {
            colorData: true,
            gridData: true,
            versions: true,
          },
        },
      },
      orderBy: { updatedAt: 'desc' },
      skip: pagination.offset,
      take: limit,
    });

    const projectResponses: (ProjectResponse & { 
      user?: any; 
      stats?: any; 
    })[] = projects.map(project => ({
      id: project.id,
      name: project.name,
      description: project.description || undefined,
      userId: project.userId,
      createdAt: project.createdAt.toISOString(),
      updatedAt: project.updatedAt.toISOString(),
      user: project.user,
      stats: {
        colorDataCount: project._count.colorData,
        gridDataCount: project._count.gridData,
        versionCount: project._count.versions,
      },
    }));

    return successResponse({
      projects: projectResponses,
      pagination: {
        page: pagination.page,
        limit: pagination.limit,
        total: pagination.total,
        totalPages: pagination.totalPages,
      },
    });
  } catch (error) {
    return handleDatabaseError(error);
  }
});

// POST /api/projects - 创建新项目
export const POST = withErrorHandling(async (request: NextRequest) => {
  const body = await parseRequestBody<CreateProjectRequest & { userId: string }>(request);
  
  if (!body) {
    return errorResponse('无效的请求体');
  }

  // 验证必需字段
  const requiredErrors = validateRequired(body, ['name', 'userId']);
  if (requiredErrors.length > 0) {
    return validationErrorResponse(requiredErrors);
  }

  // 验证数据
  const errors: string[] = [];
  
  errors.push(...validateStringLength(body.name, 'name', 1, 100));
  
  if (body.description) {
    errors.push(...validateStringLength(body.description, 'description', 0, 500));
  }

  if (errors.length > 0) {
    return validationErrorResponse(errors);
  }

  try {
    // 检查用户是否存在
    const user = await prisma.user.findUnique({
      where: { id: body.userId },
    });

    if (!user) {
      return errorResponse('用户不存在', 404);
    }

    // 创建项目
    const project = await prisma.project.create({
      data: {
        name: body.name,
        description: body.description,
        userId: body.userId,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    // 创建默认项目设置
    await prisma.projectSettings.create({
      data: {
        projectId: project.id,
        matrixStyles: JSON.stringify({}),
        colorScheme: JSON.stringify({}),
      },
    });

    // 创建默认组合数据
    await prisma.combinationData.create({
      data: {
        projectId: project.id,
        selectedGroups: JSON.stringify({}),
        swapGroupsData: JSON.stringify({}),
        modeActivation: JSON.stringify({}),
        operationHistory: JSON.stringify([]),
      },
    });

    const projectResponse: ProjectResponse & { user?: any } = {
      id: project.id,
      name: project.name,
      description: project.description || undefined,
      userId: project.userId,
      createdAt: project.createdAt.toISOString(),
      updatedAt: project.updatedAt.toISOString(),
      user: project.user,
    };

    return successResponse(projectResponse, '项目创建成功');
  } catch (error) {
    return handleDatabaseError(error);
  }
});
