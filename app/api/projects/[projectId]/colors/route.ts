/**
 * 颜色数据API - /api/projects/[projectId]/colors
 * 🎯 核心功能：颜色坐标数据的CRUD操作
 * 🔄 RESTful设计：GET, POST, PUT, DELETE
 * ⚡ 批量操作：支持批量创建和更新
 */

import { NextRequest } from 'next/server';
import prisma from '@/lib/prisma';
import {
  successResponse,
  errorResponse,
  validationErrorResponse,
  notFoundResponse,
  parseRequestBody,
  getQueryParams,
  validateRequired,
  validateEnum,
  validateNumberRange,
  validateCoordinates,
  withErrorHandling,
  handleDatabaseError,
} from '@/lib/api-utils';
import { ColorDataRequest, ColorDataResponse, ColorDataQuery } from '@/types/api';

const COLOR_TYPES = ['red', 'cyan', 'yellow', 'purple', 'orange', 'green', 'blue', 'pink'];
const LEVELS = [1, 2, 3, 4];

// GET /api/projects/[projectId]/colors - 获取项目的颜色数据
export const GET = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: { projectId: string } }
) => {
  const { projectId } = params;
  const searchParams = getQueryParams(request);
  
  // 解析查询参数
  const query: ColorDataQuery = {
    colorType: searchParams.get('colorType') || undefined,
    level: searchParams.get('level') ? parseInt(searchParams.get('level')!, 10) : undefined,
    visible: searchParams.get('visible') ? searchParams.get('visible') === 'true' : undefined,
  };

  try {
    // 检查项目是否存在
    const project = await prisma.project.findUnique({
      where: { id: projectId },
    });

    if (!project) {
      return notFoundResponse('项目');
    }

    // 构建查询条件
    const where: any = { projectId };
    
    if (query.colorType) {
      where.colorType = query.colorType;
    }
    
    if (query.level) {
      where.level = query.level;
    }
    
    if (query.visible !== undefined) {
      where.visible = query.visible;
    }

    // 获取颜色数据
    const colorData = await prisma.colorData.findMany({
      where,
      orderBy: [
        { colorType: 'asc' },
        { level: 'asc' },
      ],
    });

    const colorResponses: ColorDataResponse[] = colorData.map(data => ({
      id: data.id,
      projectId: data.projectId,
      colorType: data.colorType,
      level: data.level,
      coordinates: JSON.parse(data.coordinates),
      colorValue: data.colorValue,
      visible: data.visible,
      createdAt: data.createdAt.toISOString(),
      updatedAt: data.updatedAt.toISOString(),
    }));

    return successResponse(colorResponses);
  } catch (error) {
    return handleDatabaseError(error);
  }
});

// POST /api/projects/[projectId]/colors - 创建颜色数据
export const POST = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: { projectId: string } }
) => {
  const { projectId } = params;
  const body = await parseRequestBody<ColorDataRequest>(request);
  
  if (!body) {
    return errorResponse('无效的请求体');
  }

  // 验证必需字段
  const requiredErrors = validateRequired(body, ['colorType', 'level', 'coordinates', 'colorValue']);
  if (requiredErrors.length > 0) {
    return validationErrorResponse(requiredErrors);
  }

  // 验证数据
  const errors: string[] = [];
  
  errors.push(...validateEnum(body.colorType, 'colorType', COLOR_TYPES));
  errors.push(...validateNumberRange(body.level, 'level', 1, 4));
  errors.push(...validateCoordinates(body.coordinates));

  if (errors.length > 0) {
    return validationErrorResponse(errors);
  }

  try {
    // 检查项目是否存在
    const project = await prisma.project.findUnique({
      where: { id: projectId },
    });

    if (!project) {
      return notFoundResponse('项目');
    }

    // 创建或更新颜色数据
    const colorData = await prisma.colorData.upsert({
      where: {
        projectId_colorType_level: {
          projectId,
          colorType: body.colorType,
          level: body.level,
        },
      },
      update: {
        coordinates: JSON.stringify(body.coordinates),
        colorValue: body.colorValue,
        visible: body.visible ?? true,
      },
      create: {
        projectId,
        colorType: body.colorType,
        level: body.level,
        coordinates: JSON.stringify(body.coordinates),
        colorValue: body.colorValue,
        visible: body.visible ?? true,
      },
    });

    const colorResponse: ColorDataResponse = {
      id: colorData.id,
      projectId: colorData.projectId,
      colorType: colorData.colorType,
      level: colorData.level,
      coordinates: JSON.parse(colorData.coordinates),
      colorValue: colorData.colorValue,
      visible: colorData.visible,
      createdAt: colorData.createdAt.toISOString(),
      updatedAt: colorData.updatedAt.toISOString(),
    };

    return successResponse(colorResponse, '颜色数据保存成功');
  } catch (error) {
    return handleDatabaseError(error);
  }
});
