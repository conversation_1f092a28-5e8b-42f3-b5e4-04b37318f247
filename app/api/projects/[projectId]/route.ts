/**
 * 单个项目API - /api/projects/[projectId]
 * 🎯 核心功能：获取、更新、删除特定项目
 * 🔄 RESTful设计：GET, PUT, DELETE
 * ⚡ 类型安全：完整的TypeScript支持
 */

import { NextRequest } from 'next/server';
import prisma from '@/lib/prisma';
import {
  successResponse,
  errorResponse,
  validationErrorResponse,
  notFoundResponse,
  parseRequestBody,
  validateStringLength,
  withErrorHandling,
  handleDatabaseError,
} from '@/lib/api-utils';
import { CreateProjectRequest, ProjectResponse } from '@/types/api';

// GET /api/projects/[projectId] - 获取特定项目
export const GET = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: { projectId: string } }
) => {
  const { projectId } = params;

  if (!projectId) {
    return errorResponse('项目ID是必需的');
  }

  try {
    const project = await prisma.project.findUnique({
      where: { id: projectId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        _count: {
          select: {
            colorData: true,
            gridData: true,
            blackCellData: true,
            versions: true,
          },
        },
        settings: true,
        combinationData: true,
      },
    });

    if (!project) {
      return notFoundResponse('项目');
    }

    const projectResponse: ProjectResponse & { 
      user?: any; 
      stats?: any;
      settings?: any;
      combinationData?: any;
    } = {
      id: project.id,
      name: project.name,
      description: project.description || undefined,
      userId: project.userId,
      createdAt: project.createdAt.toISOString(),
      updatedAt: project.updatedAt.toISOString(),
      user: project.user,
      stats: {
        colorDataCount: project._count.colorData,
        gridDataCount: project._count.gridData,
        blackCellDataCount: project._count.blackCellData,
        versionCount: project._count.versions,
      },
      settings: project.settings,
      combinationData: project.combinationData,
    };

    return successResponse(projectResponse);
  } catch (error) {
    return handleDatabaseError(error);
  }
});

// PUT /api/projects/[projectId] - 更新项目
export const PUT = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: { projectId: string } }
) => {
  const { projectId } = params;

  if (!projectId) {
    return errorResponse('项目ID是必需的');
  }

  const body = await parseRequestBody<Partial<CreateProjectRequest>>(request);
  
  if (!body) {
    return errorResponse('无效的请求体');
  }

  // 验证数据
  const errors: string[] = [];
  
  if (body.name !== undefined) {
    errors.push(...validateStringLength(body.name, 'name', 1, 100));
  }
  
  if (body.description !== undefined && body.description) {
    errors.push(...validateStringLength(body.description, 'description', 0, 500));
  }

  if (errors.length > 0) {
    return validationErrorResponse(errors);
  }

  try {
    // 检查项目是否存在
    const existingProject = await prisma.project.findUnique({
      where: { id: projectId },
    });

    if (!existingProject) {
      return notFoundResponse('项目');
    }

    // 更新项目
    const project = await prisma.project.update({
      where: { id: projectId },
      data: {
        name: body.name,
        description: body.description,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    const projectResponse: ProjectResponse & { user?: any } = {
      id: project.id,
      name: project.name,
      description: project.description || undefined,
      userId: project.userId,
      createdAt: project.createdAt.toISOString(),
      updatedAt: project.updatedAt.toISOString(),
      user: project.user,
    };

    return successResponse(projectResponse, '项目更新成功');
  } catch (error) {
    return handleDatabaseError(error);
  }
});

// DELETE /api/projects/[projectId] - 删除项目
export const DELETE = withErrorHandling(async (
  request: NextRequest,
  { params }: { params: { projectId: string } }
) => {
  const { projectId } = params;

  if (!projectId) {
    return errorResponse('项目ID是必需的');
  }

  try {
    // 检查项目是否存在
    const existingProject = await prisma.project.findUnique({
      where: { id: projectId },
    });

    if (!existingProject) {
      return notFoundResponse('项目');
    }

    // 删除项目（级联删除相关数据）
    // Prisma schema中已配置了 onDelete: Cascade，所以相关数据会自动删除
    await prisma.project.delete({
      where: { id: projectId },
    });

    return successResponse(null, '项目删除成功');
  } catch (error) {
    return handleDatabaseError(error);
  }
});
