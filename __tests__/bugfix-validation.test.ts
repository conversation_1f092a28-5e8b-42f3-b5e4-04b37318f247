/**
 * Bug修复验证测试
 * 🎯 核心价值：验证调试日志、颜色渲染、分组控制的修复效果
 * ⚡ 测试范围：LogManager、ColorRenderer、GroupManager的功能正确性
 * 📊 验证内容：控制台spam修复、颜色映射一致性、分组独立控制
 */

import { logger, LogLevel, LogManager } from '../utils/LogManager';
import { ColorRenderer } from '../utils/colorRenderer';
import { GroupManager } from '../utils/groupManager';
import { groupIndependencyValidator } from '../utils/groupIndependencyValidator';
import { CellDataManager } from '../utils/CellDataManager';
import { createCellData, initializeGridData } from '../utils/cellDataHelpers';

// Mock window对象
Object.defineProperty(window, 'sessionStorage', {
  value: {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
  },
  writable: true,
});

describe('Bug修复验证测试套件', () => {
  let cellManager: CellDataManager;
  let colorRenderer: ColorRenderer;
  let groupManager: GroupManager;

  beforeEach(() => {
    // 重置单例实例
    cellManager = CellDataManager.getInstance();
    colorRenderer = ColorRenderer.getInstance();
    groupManager = GroupManager.getInstance();
    
    // 清理数据
    cellManager.clear();
    
    // 重置日志配置
    logger.setEnabled(true);
    logger.setLogLevel(LogLevel.DEBUG);
  });

  describe('优先级1: 调试日志问题修复验证', () => {
    test('LogManager应该正确控制日志输出', () => {
      // 测试精简模式
      logger.enableCompactMode();
      const config = logger.getConfig();
      
      expect(config.compactMode).toBe(true);
      expect(config.level).toBe(LogLevel.WARN);
    });

    test('LogManager应该支持坐标过滤', () => {
      logger.setCoordinateFilter('8,0');
      const config = logger.getConfig();
      
      expect(config.coordinateFilter).toBe('8,0');
    });

    test('LogManager应该支持模块过滤', () => {
      logger.setModuleFilter(['color', 'group']);
      const config = logger.getConfig();
      
      expect(config.moduleFilter).toEqual(['color', 'group']);
    });

    test('LogManager应该记录日志历史', () => {
      logger.setLogLevel(LogLevel.INFO); // 确保info级别可以记录
      logger.setCoordinateFilter(null); // 清除坐标过滤
      logger.setModuleFilter(null); // 清除模块过滤
      logger.clearHistory(); // 在设置完配置后清除历史

      const initialCount = logger.getHistory().length;
      logger.info('general', '测试消息');

      const history = logger.getHistory();
      expect(history.length).toBe(initialCount + 1);
      expect(history[history.length - 1].message).toBe('测试消息');
    });

    test('LogManager应该提供统计信息', () => {
      logger.setLogLevel(LogLevel.DEBUG); // 确保所有级别都能记录
      logger.setCoordinateFilter(null); // 清除坐标过滤
      logger.setModuleFilter(null); // 清除模块过滤
      logger.clearHistory(); // 在设置完配置后清除历史

      const initialStats = logger.getStats();
      logger.error('general', '错误消息');
      logger.warn('color', '警告消息');
      logger.info('group', '信息消息');

      const stats = logger.getStats();
      expect(stats.total).toBeGreaterThanOrEqual(initialStats.total + 3);
      expect(stats.byLevel.ERROR).toBeGreaterThanOrEqual(1);
      expect(stats.byLevel.WARN).toBeGreaterThanOrEqual(1);
      expect(stats.byLevel.INFO).toBeGreaterThanOrEqual(1);
      expect(stats.byModule.general).toBeGreaterThanOrEqual(1);
      expect(stats.byModule.color).toBeGreaterThanOrEqual(1);
      expect(stats.byModule.group).toBeGreaterThanOrEqual(1);
    });
  });

  describe('优先级2: 颜色渲染问题修复验证', () => {
    test('ColorRenderer应该正确处理colorMappingValue', () => {
      const cellData = createCellData(0, 0, 1, {
        color: 'red',
        level: 1,
        colorMappingValue: 80, // 使用一个会产生明显变化的值
      });

      const cell = cellManager.createCell(cellData);
      const mappedColor = colorRenderer.calculateMappedColor(cell);

      expect(mappedColor).toBeDefined();
      expect(mappedColor).not.toBe('#FF0000'); // 应该是调整后的颜色
    });

    test('ColorRenderer应该正确计算单元格样式', () => {
      const cellData = createCellData(0, 0, 1, {
        color: 'red',
        level: 1,
        colorMappingValue: 25,
      });
      
      const cell = cellManager.createCell(cellData);
      const style = colorRenderer.calculateCellStyle(cell);
      
      expect(style).toBeDefined();
      expect(style.backgroundColor).toBeDefined();
      expect(style.color).toBeDefined();
      expect(style.opacity).toBeDefined();
    });

    test('ColorRenderer应该支持批量样式计算', () => {
      const cells = [];
      for (let i = 0; i < 5; i++) {
        const cellData = createCellData(0, i, i, {
          color: 'red',
          level: 1,
          colorMappingValue: i * 10,
        });
        cells.push(cellManager.createCell(cellData));
      }
      
      const styles = colorRenderer.batchCalculateStyles(cells);
      
      expect(styles.size).toBe(5);
      cells.forEach(cell => {
        expect(styles.has(cell.id)).toBe(true);
      });
    });

    test('ColorRenderer应该正确处理级别可见性', () => {
      const cellData = createCellData(0, 0, 1, {
        color: 'red',
        level: 1,
      });
      
      const cell = cellManager.createCell(cellData);
      
      // 设置级别不可见
      colorRenderer.setColorConfig('red', { showLevel1: false });
      const effectiveColor = colorRenderer.getEffectiveColor(cell);
      
      expect(effectiveColor).toBe('#000000'); // 应该返回黑色
    });
  });

  describe('优先级3: 分组控制问题修复验证', () => {
    test('GroupManager应该正确分离十字组和交叉组', () => {
      const crossGroups = groupManager.getGroupsByType('cross');
      const diagonalGroups = groupManager.getGroupsByType('diagonal');
      
      expect(crossGroups.length).toBe(10); // 1-10
      expect(diagonalGroups.length).toBe(34); // 11-44
      
      crossGroups.forEach(group => {
        expect(group.id).toBeGreaterThanOrEqual(1);
        expect(group.id).toBeLessThanOrEqual(10);
        expect(group.type).toBe('cross');
      });
      
      diagonalGroups.forEach(group => {
        expect(group.id).toBeGreaterThanOrEqual(11);
        expect(group.id).toBeLessThanOrEqual(44);
        expect(group.type).toBe('diagonal');
      });
    });

    test('GroupManager应该支持独立激活十字组和交叉组', () => {
      // 重置所有分组
      groupManager.deactivateAllGroups();
      
      // 独立激活十字组和交叉组
      const success = groupManager.activateGroupsIndependently(1, 11);
      
      expect(success).toBe(true);
      
      const activeGroups = groupManager.getActiveGroups();
      expect(activeGroups.length).toBe(2);
      
      const activeCross = activeGroups.find(g => g.type === 'cross');
      const activeDiagonal = activeGroups.find(g => g.type === 'diagonal');
      
      expect(activeCross).toBeDefined();
      expect(activeCross?.id).toBe(1);
      expect(activeDiagonal).toBeDefined();
      expect(activeDiagonal?.id).toBe(11);
    });

    test('GroupManager应该支持按类型取消激活', () => {
      // 激活多个分组
      groupManager.activateGroup(1);
      groupManager.activateGroup(2);
      groupManager.activateGroup(11);
      
      // 只取消十字组
      groupManager.deactivateGroupsByType('cross');
      
      const activeGroups = groupManager.getActiveGroups();
      const activeCross = activeGroups.filter(g => g.type === 'cross');
      const activeDiagonal = activeGroups.filter(g => g.type === 'diagonal');
      
      expect(activeCross.length).toBe(0);
      expect(activeDiagonal.length).toBe(1);
      expect(activeDiagonal[0].id).toBe(11);
    });

    test('GroupManager应该支持独立的可见性控制', () => {
      // 隐藏所有十字组
      groupManager.hideAllCrossGroups();
      
      const visibleGroups = groupManager.getVisibleGroups();
      const visibleCross = visibleGroups.filter(g => g.type === 'cross');
      const visibleDiagonal = visibleGroups.filter(g => g.type === 'diagonal');
      
      expect(visibleCross.length).toBe(0);
      expect(visibleDiagonal.length).toBeGreaterThan(0);
      
      // 恢复十字组可见性
      groupManager.showAllCrossGroups();
      
      const restoredGroups = groupManager.getVisibleGroups();
      const restoredCross = restoredGroups.filter(g => g.type === 'cross');
      
      expect(restoredCross.length).toBe(10);
    });
  });

  describe('分组独立性验证', () => {
    test('应该通过分组独立性验证', () => {
      const report = groupIndependencyValidator.validateGroupIndependency();
      
      expect(report).toBeDefined();
      expect(report.overallScore).toBeGreaterThanOrEqual(70); // 至少70分
      expect(report.passedTests).toBeGreaterThan(report.failedTests);
      expect(report.results.length).toBe(10); // 10项测试
    });

    test('基础分离测试应该通过', () => {
      const report = groupIndependencyValidator.validateGroupIndependency();
      const basicSeparationTest = report.results.find(r => r.testName === '基础分离测试');
      
      expect(basicSeparationTest).toBeDefined();
      expect(basicSeparationTest?.passed).toBe(true);
      expect(basicSeparationTest?.score).toBe(100);
    });

    test('激活独立性测试应该通过', () => {
      const report = groupIndependencyValidator.validateGroupIndependency();
      const activationTest = report.results.find(r => r.testName === '激活独立性测试');
      
      expect(activationTest).toBeDefined();
      expect(activationTest?.passed).toBe(true);
      expect(activationTest?.score).toBeGreaterThanOrEqual(90);
    });
  });

  describe('数据一致性验证', () => {
    test('CellData应该包含所有必需字段', () => {
      const cellData = createCellData(0, 0, 1, {
        color: 'red',
        level: 1,
        group: 1,
        colorMappingValue: 50,
      });
      
      const cell = cellManager.createCell(cellData);
      
      expect(cell.id).toBeDefined();
      expect(cell.row).toBe(0);
      expect(cell.column).toBe(0);
      expect(cell.x).toBeDefined();
      expect(cell.y).toBeDefined();
      expect(cell.index).toBe(1);
      expect(cell.color).toBe('#FF0000');
      expect(cell.colorMappingValue).toBe(50);
      expect(cell.level).toBe(1);
      expect(cell.group).toBe(1);
      expect(cell.number).toBe(1); // 向后兼容
      expect(cell.col).toBe(0); // 向后兼容
    });

    test('initializeGridData应该正确设置colorMappingValue', () => {
      initializeGridData(cellManager);

      const allCells = cellManager.getAllCells();
      expect(allCells.length).toBe(33 * 33);

      // 检查colorMappingValue是否被正确设置（第一个单元格可能是0，这是正常的）
      const firstCell = allCells[0];
      expect(firstCell.colorMappingValue).toBeGreaterThanOrEqual(0);

      // 检查第二个单元格（应该是2）
      const secondCell = allCells[1];
      expect(secondCell.colorMappingValue).toBe(2); // (row=0, col=1, index=1) = (0+1+1) = 2

      // 检查一个中间的单元格
      const middleCell = allCells[100]; // 大约在(3,1)位置
      expect(middleCell.colorMappingValue).toBeGreaterThan(0);
    });
  });
});
