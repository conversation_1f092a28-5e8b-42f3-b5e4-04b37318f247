/**
 * 重构演示文件
 * 🎯 展示基于统一CellData结构的新架构使用方法
 * ⚡ 演示CellDataManager、ColorRenderer、GroupManager的集成使用
 * 📊 提供完整的数据管理和UI控制示例
 */

import React, { useEffect, useState } from 'react';
import { useCellDataManager } from '../hooks/useCellDataManager';
import { useColorRenderer } from '../hooks/useColorRenderer';
import { useGroupManager } from '../hooks/useGroupManager';
import { createCellData, COLOR_HEX_MAP } from '../utils/cellDataHelpers';
import type { ColorType } from '../utils/CellDataManager';

export const RefactoredDemo: React.FC = () => {
  const [isInitialized, setIsInitialized] = useState(false);

  // 使用新的数据管理系统
  const {
    cells,
    stats,
    createCell,
    updateCell,
    getCellByPosition,
    batchUpdateColors,
    initializeGrid,
    error: dataError,
    isLoading: dataLoading,
  } = useCellDataManager();

  // 使用颜色渲染系统
  const {
    calculateCellStyle,
    calculateCellContent,
    toggleColorVisibility,
    setColorOpacity,
    showAllColors,
    hideAllColors,
    getColorConfig,
  } = useColorRenderer();

  // 使用分组管理系统
  const {
    stats: groupStats,
    activateGroup,
    showAllCrossGroups,
    hideAllCrossGroups,
    addCellsToGroup,
    clearAllGroups,
    error: groupError,
  } = useGroupManager();

  // 初始化演示数据
  useEffect(() => {
    if (!isInitialized) {
      console.log('🚀 初始化重构后的数据系统...');
      
      // 初始化33x33网格
      initializeGrid();
      
      // 创建一些示例单元格
      const sampleCells = [
        createCellData(5, 5, 170, { color: 'red', level: 1, group: 1 }),
        createCellData(10, 10, 340, { color: 'blue', level: 2, group: 2 }),
        createCellData(15, 15, 510, { color: 'green', level: 3, group: 11 }),
      ];

      sampleCells.forEach(cellData => {
        createCell(cellData);
      });

      setIsInitialized(true);
      console.log('✅ 数据系统初始化完成');
    }
  }, [isInitialized, initializeGrid, createCell]);

  // 演示颜色控制
  const handleColorDemo = () => {
    console.log('🎨 演示颜色控制功能...');
    
    // 批量更新红色单元格
    const redCells = cells.filter(cell => cell.color === COLOR_HEX_MAP.red);
    const redCellIds = redCells.map(cell => cell.id);
    
    if (redCellIds.length > 0) {
      batchUpdateColors(redCellIds, 'cyan', 2);
      console.log(`✅ 已将${redCellIds.length}个红色单元格更新为青色`);
    }

    // 调整颜色透明度
    setColorOpacity('blue', 0.7);
    console.log('✅ 已调整蓝色透明度为0.7');
  };

  // 演示分组控制
  const handleGroupDemo = () => {
    console.log('🔗 演示分组控制功能...');
    
    // 激活第一个十字组
    activateGroup(1);
    console.log('✅ 已激活十字组1');

    // 将一些单元格添加到分组
    const centerCells = [
      getCellByPosition(16, 16),
      getCellByPosition(16, 17),
      getCellByPosition(17, 16),
      getCellByPosition(17, 17),
    ].filter(Boolean).map(cell => cell!.id);

    if (centerCells.length > 0) {
      addCellsToGroup(centerCells, 1);
      console.log(`✅ 已将${centerCells.length}个中心单元格添加到分组1`);
    }
  };

  // 演示样式计算
  const handleStyleDemo = () => {
    console.log('🎭 演示样式计算功能...');
    
    const sampleCell = getCellByPosition(10, 10);
    if (sampleCell) {
      const style = calculateCellStyle(sampleCell, true);
      const content = calculateCellContent(sampleCell, 'coordinate');
      
      console.log('✅ 单元格样式:', style);
      console.log('✅ 单元格内容:', content);
    }
  };

  // 演示批量操作
  const handleBatchDemo = () => {
    console.log('⚡ 演示批量操作功能...');
    
    // 显示所有颜色
    showAllColors();
    console.log('✅ 已显示所有颜色');

    // 显示所有十字组
    showAllCrossGroups();
    console.log('✅ 已显示所有十字组');

    setTimeout(() => {
      // 2秒后隐藏所有颜色
      hideAllColors();
      console.log('✅ 已隐藏所有颜色');
    }, 2000);
  };

  if (dataLoading) {
    return (
      <div className="p-6 bg-gray-50 rounded-lg">
        <div className="text-center">
          <div className="text-lg font-medium text-gray-700">加载中...</div>
          <div className="text-sm text-gray-500 mt-2">正在初始化重构后的数据系统</div>
        </div>
      </div>
    );
  }

  if (dataError || groupError) {
    return (
      <div className="p-6 bg-red-50 rounded-lg border border-red-200">
        <div className="text-lg font-medium text-red-800">错误</div>
        <div className="text-sm text-red-600 mt-2">{dataError || groupError}</div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
        <h1 className="text-xl font-bold text-blue-800 mb-2">🎉 重构完成演示</h1>
        <p className="text-blue-600 text-sm">
          基于统一CellData结构的新架构已经完成！下面是各个系统的演示功能。
        </p>
      </div>

      {/* 统计信息 */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg p-4 border shadow-sm">
          <div className="text-2xl font-bold text-gray-800">{stats.totalCells}</div>
          <div className="text-sm text-gray-600">总单元格数</div>
        </div>
        <div className="bg-white rounded-lg p-4 border shadow-sm">
          <div className="text-2xl font-bold text-purple-600">{groupStats.totalGroups}</div>
          <div className="text-sm text-gray-600">总分组数</div>
        </div>
        <div className="bg-white rounded-lg p-4 border shadow-sm">
          <div className="text-2xl font-bold text-green-600">{groupStats.activeGroups}</div>
          <div className="text-sm text-gray-600">活跃分组</div>
        </div>
        <div className="bg-white rounded-lg p-4 border shadow-sm">
          <div className="text-2xl font-bold text-orange-600">{groupStats.cellsInGroups}</div>
          <div className="text-sm text-gray-600">分组中的格子</div>
        </div>
      </div>

      {/* 演示按钮 */}
      <div className="bg-white rounded-lg p-6 border shadow-sm">
        <h2 className="text-lg font-semibold text-gray-800 mb-4">功能演示</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          <button
            onClick={handleColorDemo}
            className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors text-sm"
          >
            🎨 颜色控制
          </button>
          <button
            onClick={handleGroupDemo}
            className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors text-sm"
          >
            🔗 分组控制
          </button>
          <button
            onClick={handleStyleDemo}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors text-sm"
          >
            🎭 样式计算
          </button>
          <button
            onClick={handleBatchDemo}
            className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors text-sm"
          >
            ⚡ 批量操作
          </button>
        </div>
      </div>

      {/* 架构说明 */}
      <div className="bg-gray-50 rounded-lg p-6 border">
        <h2 className="text-lg font-semibold text-gray-800 mb-4">🏗️ 新架构特点</h2>
        <div className="grid md:grid-cols-2 gap-4 text-sm">
          <div>
            <h3 className="font-medium text-gray-700 mb-2">✅ 已完成的重构</h3>
            <ul className="space-y-1 text-gray-600">
              <li>• 统一CellData数据结构</li>
              <li>• CellDataManager数据管理层</li>
              <li>• ColorRenderer颜色渲染系统</li>
              <li>• GroupManager分组管理系统</li>
              <li>• React Hooks集成</li>
              <li>• UI组件重构</li>
            </ul>
          </div>
          <div>
            <h3 className="font-medium text-gray-700 mb-2">🚀 架构优势</h3>
            <ul className="space-y-1 text-gray-600">
              <li>• 类型安全的TypeScript支持</li>
              <li>• 标准化的CRUD操作</li>
              <li>• 高性能的缓存机制</li>
              <li>• 独立的分组控制</li>
              <li>• 响应式数据管理</li>
              <li>• 可维护的代码结构</li>
            </ul>
          </div>
        </div>
      </div>

      {/* 颜色配置演示 */}
      <div className="bg-white rounded-lg p-6 border shadow-sm">
        <h2 className="text-lg font-semibold text-gray-800 mb-4">🎨 颜色配置状态</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {(['red', 'cyan', 'yellow', 'purple', 'orange', 'green', 'blue', 'pink'] as ColorType[]).map(color => {
            const config = getColorConfig(color);
            return (
              <div key={color} className="p-3 border rounded">
                <div className="flex items-center space-x-2 mb-2">
                  <div 
                    className="w-4 h-4 rounded"
                    style={{ backgroundColor: COLOR_HEX_MAP[color] }}
                  />
                  <span className="text-sm font-medium">{color}</span>
                </div>
                <div className="text-xs text-gray-600">
                  <div>可见: {config.showCells ? '是' : '否'}</div>
                  <div>透明度: {config.opacity}</div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};
