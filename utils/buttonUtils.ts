/**
 * 按钮工具函数 - 完整实现版
 * 🎯 职责：提供各种按钮样式生成功能
 * 📦 数据源：基于样式常量和类型定义
 * ✅ 状态：完整实现，支持所有按钮样式需求
 */

import type { CellData } from '../types/grid';
import type { BasicColorType } from '../constants/colors';
import {
  type ButtonVariant,
  type ButtonSize,
  BUTTON_STYLES,
  TAB_STYLES,
  GRID_STYLES,
  SIZE_STYLES,
  VARIANT_STYLES,
  BASE_BUTTON_STYLES,
} from '../constants/styles';

/**
 * 获取基础按钮样式
 * @param variant 按钮变体键名
 * @returns CSS类名字符串
 */
export const getButtonStyle = (variant: keyof typeof BUTTON_STYLES): string => {
  return BUTTON_STYLES[variant] || BUTTON_STYLES.inactive;
};

/**
 * 获取Tab样式
 * @param active 是否为激活状态
 * @returns CSS类名字符串
 */
export const getTabStyle = (active: boolean): string => {
  return active ? TAB_STYLES.active : TAB_STYLES.inactive;
};

/**
 * 获取网格样式
 * @param cols 列数
 * @returns CSS类名字符串
 */
export const getGridStyle = (cols: 2 | 3 | 4 | 5): string => {
  const colsMap = {
    2: 'cols2',
    3: 'cols3',
    4: 'cols4',
    5: 'cols5',
  } as const;
  return GRID_STYLES[colsMap[cols]] || GRID_STYLES.cols3;
};

/**
 * 获取高级按钮样式（组合变体和尺寸）
 * @param variant 按钮变体
 * @param size 按钮尺寸
 * @param extraClasses 额外CSS类
 * @returns CSS类名字符串
 */
export const getAdvancedButtonStyle = (
  variant: ButtonVariant,
  size: ButtonSize,
  extraClasses?: string
): string => {
  const baseStyle = BASE_BUTTON_STYLES;
  const variantStyle = VARIANT_STYLES[variant] || VARIANT_STYLES.secondary;
  const sizeStyle = SIZE_STYLES[size] || SIZE_STYLES.md;

  return [baseStyle, variantStyle, sizeStyle, extraClasses]
    .filter(Boolean)
    .join(' ');
};

/**
 * 获取激活状态按钮样式
 * @param isActive 是否激活
 * @param size 按钮尺寸
 * @param extraClasses 额外CSS类
 * @returns CSS类名字符串
 */
export const getActiveButtonStyle = (
  isActive: boolean,
  size: ButtonSize = 'md',
  extraClasses?: string
): string => {
  const variant: ButtonVariant = isActive ? 'active' : 'inactive';
  return getAdvancedButtonStyle(variant, size, extraClasses);
};

/**
 * 获取颜色按钮样式
 * @param isSelected 是否选中
 * @param colorClass 颜色CSS类
 * @param size 按钮尺寸
 * @returns CSS类名字符串
 */
export const getColorButtonStyle = (
  isSelected: boolean,
  colorClass?: string,
  size: ButtonSize = 'md'
): string => {
  const baseStyle = getAdvancedButtonStyle(
    isSelected ? 'primary' : 'secondary',
    size
  );

  if (colorClass && isSelected) {
    return `${baseStyle} ${colorClass}`;
  }

  return baseStyle;
};

/**
 * 获取网格按钮样式
 * @param isSelected 是否选中
 * @param color 颜色类型
 * @param size 按钮尺寸
 * @returns CSS类名字符串
 */
export const getGridButtonStyle = (
  isSelected: boolean,
  color: BasicColorType | 'gray' = 'gray',
  size: ButtonSize = 'md'
): string => {
  const variant: ButtonVariant = isSelected ? 'active' : 'inactive';
  const baseStyle = getAdvancedButtonStyle(variant, size);

  // 根据颜色添加特定样式
  const colorStyles = {
    red: 'border-red-300 hover:border-red-400',
    cyan: 'border-cyan-300 hover:border-cyan-400',
    yellow: 'border-yellow-300 hover:border-yellow-400',
    purple: 'border-purple-300 hover:border-purple-400',
    orange: 'border-orange-300 hover:border-orange-400',
    green: 'border-green-300 hover:border-green-400',
    blue: 'border-blue-300 hover:border-blue-400',
    pink: 'border-pink-300 hover:border-pink-400',
    black: 'border-gray-600 hover:border-gray-700',
    gray: 'border-gray-300 hover:border-gray-400',
  };

  const colorStyle = colorStyles[color] || colorStyles.gray;
  return `${baseStyle} ${colorStyle}`;
};

/**
 * 获取模式按钮样式
 * @param isCurrentMode 是否为当前模式
 * @param size 按钮尺寸
 * @returns CSS类名字符串
 */
export const getModeButtonStyle = (
  isCurrentMode: boolean,
  size: ButtonSize = 'md'
): string => {
  return getAdvancedButtonStyle(
    isCurrentMode ? 'primary' : 'secondary',
    size
  );
};

/**
 * 获取危险按钮样式
 * @param size 按钮尺寸
 * @param extraClasses 额外CSS类
 * @returns CSS类名字符串
 */
export const getDangerButtonStyle = (
  size: ButtonSize = 'md',
  extraClasses?: string
): string => {
  return getAdvancedButtonStyle('danger', size, extraClasses);
};

/**
 * 获取成功按钮样式
 * @param size 按钮尺寸
 * @param extraClasses 额外CSS类
 * @returns CSS类名字符串
 */
export const getSuccessButtonStyle = (
  size: ButtonSize = 'md',
  extraClasses?: string
): string => {
  return getAdvancedButtonStyle('success', size, extraClasses);
};

/**
 * 临时占位函数 - 保持向后兼容
 */
export const getButtonStylePlaceholder = (): string => {
  return getAdvancedButtonStyle('secondary', 'md');
};