/**
 * CellData辅助工具函数
 * 🎯 核心价值：提供便捷的单元格数据操作和计算函数
 * ⚡ 性能优化：缓存计算结果、批量操作优化
 * 📊 功能范围：颜色计算、坐标转换、分组判断、级别管理
 */

import type { CellData } from '../types/grid';
import { CellDataManager, type ColorType, type LevelType, type GroupType } from './CellDataManager';

// 颜色映射配置
export const COLOR_HEX_MAP: Record<ColorType, string> = {
  red: '#FF0000',
  cyan: '#00FFFF',
  yellow: '#FFFF00',
  purple: '#800080',
  orange: '#FFA500',
  green: '#008000',
  blue: '#0000FF',
  pink: '#FFC0CB',
  black: '#000000',
};

// 级别可用性配置
export const LEVEL_AVAILABILITY: Record<ColorType, LevelType[]> = {
  red: [1, 2, 3, 4],
  cyan: [1, 2, 3, 4],
  yellow: [1, 2, 3, 4],
  purple: [1, 2, 3, 4],
  orange: [1, 3, 4],
  green: [1, 3, 4],
  blue: [1, 3, 4],
  pink: [1, 3, 4],
  black: [1], // 黑色只有一个级别
};

// 分组范围配置
export const GROUP_RANGES = {
  cross: { min: 1, max: 10 }, // 十字组
  diagonal: { min: 11, max: 44 }, // 交叉组
} as const;

/**
 * 创建标准单元格数据
 */
export function createCellData(
  row: number,
  column: number,
  index: number,
  options: {
    color?: ColorType;
    level?: LevelType;
    group?: number;
    colorMappingValue?: number;
    x?: number;
    y?: number;
  } = {}
): Omit<CellData, 'id'> {
  const {
    color = 'black',
    level = 1,
    group = 0,
    colorMappingValue = 0,
    x = column * 20, // 默认单元格大小20px
    y = row * 20,
  } = options;

  return {
    row,
    column,
    x,
    y,
    index,
    color: COLOR_HEX_MAP[color] || COLOR_HEX_MAP.black,
    colorMappingValue,
    level,
    group,
    number: index, // 向后兼容
    col: column, // 向后兼容
  };
}

/**
 * 初始化33x33网格数据
 */
export function initializeGridData(manager: CellDataManager): void {
  manager.clear();

  for (let row = 0; row < 33; row++) {
    for (let col = 0; col < 33; col++) {
      const index = row * 33 + col;
      // 计算colorMappingValue，基于位置和索引
      const colorMappingValue = calculateColorMappingValue({
        row,
        column: col,
        index,
        x: col * 20,
        y: row * 20,
      } as CellData);

      const cellData = createCellData(row, col, index, {
        colorMappingValue,
      });
      manager.createCell(cellData);
    }
  }
}

/**
 * 计算单元格的画布坐标
 */
export function calculateCellPosition(
  row: number,
  column: number,
  cellSize: number,
  margin: number = 0
): { x: number; y: number } {
  return {
    x: column * cellSize + margin,
    y: row * cellSize + margin,
  };
}

/**
 * 从画布坐标计算网格位置
 */
export function calculateGridPosition(
  x: number,
  y: number,
  cellSize: number,
  margin: number = 0
): { row: number; column: number } {
  return {
    row: Math.floor((y - margin) / cellSize),
    column: Math.floor((x - margin) / cellSize),
  };
}

/**
 * 判断分组类型
 */
export function getGroupType(group: number): GroupType | null {
  if (group >= GROUP_RANGES.cross.min && group <= GROUP_RANGES.cross.max) {
    return 'cross';
  }
  if (group >= GROUP_RANGES.diagonal.min && group <= GROUP_RANGES.diagonal.max) {
    return 'diagonal';
  }
  return null;
}

/**
 * 获取颜色类型（从十六进制值）
 */
export function getColorTypeFromHex(hex: string): ColorType | null {
  const entry = Object.entries(COLOR_HEX_MAP).find(([, value]) => value === hex);
  return entry ? (entry[0] as ColorType) : null;
}

/**
 * 检查级别是否可用
 */
export function isLevelAvailable(color: ColorType, level: LevelType): boolean {
  return LEVEL_AVAILABILITY[color]?.includes(level) ?? false;
}

/**
 * 获取颜色的可用级别
 */
export function getAvailableLevels(color: ColorType): LevelType[] {
  return LEVEL_AVAILABILITY[color] || [];
}

/**
 * 批量更新单元格颜色
 */
export function batchUpdateCellColors(
  manager: CellDataManager,
  cellIds: string[],
  color: ColorType,
  level?: LevelType
): boolean {
  const updates = cellIds.map(id => ({
    id,
    data: {
      color: COLOR_HEX_MAP[color],
      ...(level && { level }),
    },
  }));

  return manager.batchUpdate(updates);
}

/**
 * 批量更新分组
 */
export function batchUpdateCellGroups(
  manager: CellDataManager,
  cellIds: string[],
  group: number
): boolean {
  const updates = cellIds.map(id => ({
    id,
    data: { group },
  }));

  return manager.batchUpdate(updates);
}

/**
 * 获取分组中的所有单元格ID
 */
export function getGroupCellIds(manager: CellDataManager, group: number): string[] {
  return manager.getCellsByGroup(group).map(cell => cell.id);
}

/**
 * 获取指定颜色和级别的单元格
 */
export function getCellsByColorAndLevel(
  manager: CellDataManager,
  color: ColorType,
  level: LevelType
): CellData[] {
  const colorHex = COLOR_HEX_MAP[color];
  return manager.queryCells({ color: colorHex, level });
}

/**
 * 计算颜色映射值
 */
export function calculateColorMappingValue(
  cell: CellData,
  mappingFunction?: (cell: CellData) => number
): number {
  if (mappingFunction) {
    return mappingFunction(cell);
  }
  
  // 默认映射：基于位置和索引的简单计算
  return (cell.row + cell.column + cell.index) % 256;
}

/**
 * 验证单元格数据完整性
 */
export function validateCellData(cell: Partial<CellData>): string[] {
  const errors: string[] = [];

  if (typeof cell.row !== 'number' || cell.row < 0) {
    errors.push('row must be a non-negative number');
  }

  if (typeof cell.column !== 'number' || cell.column < 0) {
    errors.push('column must be a non-negative number');
  }

  if (typeof cell.index !== 'number' || cell.index < 0) {
    errors.push('index must be a non-negative number');
  }

  if (cell.color && !/^#[0-9A-Fa-f]{6}$/.test(cell.color)) {
    errors.push('color must be a valid hex color');
  }

  if (cell.level && ![1, 2, 3, 4].includes(cell.level)) {
    errors.push('level must be 1, 2, 3, or 4');
  }

  if (cell.group && (cell.group < 0 || cell.group > 44)) {
    errors.push('group must be between 0 and 44');
  }

  return errors;
}

/**
 * 创建单元格数据的深拷贝
 */
export function cloneCellData(cell: CellData): CellData {
  return {
    ...cell,
  };
}

/**
 * 比较两个单元格数据是否相等
 */
export function compareCellData(cell1: CellData, cell2: CellData): boolean {
  return (
    cell1.id === cell2.id &&
    cell1.row === cell2.row &&
    cell1.column === cell2.column &&
    cell1.x === cell2.x &&
    cell1.y === cell2.y &&
    cell1.index === cell2.index &&
    cell1.color === cell2.color &&
    cell1.colorMappingValue === cell2.colorMappingValue &&
    cell1.level === cell2.level &&
    cell1.group === cell2.group
  );
}

/**
 * 获取单元格的邻居
 */
export function getCellNeighbors(
  manager: CellDataManager,
  cell: CellData,
  includesDiagonal: boolean = false
): CellData[] {
  const neighbors: CellData[] = [];
  const directions = includesDiagonal
    ? [
        [-1, -1], [-1, 0], [-1, 1],
        [0, -1],           [0, 1],
        [1, -1],  [1, 0],  [1, 1],
      ]
    : [
        [-1, 0],
        [0, -1], [0, 1],
        [1, 0],
      ];

  directions.forEach(([dr, dc]) => {
    const newRow = cell.row + dr;
    const newCol = cell.column + dc;
    
    if (newRow >= 0 && newRow < 33 && newCol >= 0 && newCol < 33) {
      const neighbor = manager.getCellByPosition(newRow, newCol);
      if (neighbor) {
        neighbors.push(neighbor);
      }
    }
  });

  return neighbors;
}
