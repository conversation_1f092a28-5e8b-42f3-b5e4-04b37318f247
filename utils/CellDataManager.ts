/**
 * CellDataManager - 统一数据管理层
 * 🎯 核心价值：基于统一CellData结构提供标准化的CRUD操作
 * ⚡ 性能优化：内存缓存、批量操作、索引优化
 * 📊 功能范围：单元格数据管理、颜色计算、分组控制、级别管理
 * 🔄 架构设计：单例模式、事件驱动、类型安全
 */

import type { CellData } from '../types/grid';

// 分组类型定义
export type GroupType = 'cross' | 'diagonal'; // 十字组(1-10) | 交叉组(11-44)

// 颜色类型定义
export type ColorType = 'red' | 'cyan' | 'yellow' | 'purple' | 'orange' | 'green' | 'blue' | 'pink' | 'black';

// 级别类型定义
export type LevelType = 1 | 2 | 3 | 4;

// 数据变更事件类型
export interface DataChangeEvent {
  type: 'create' | 'update' | 'delete' | 'batch';
  cellIds: string[];
  timestamp: number;
}

// 查询条件接口
export interface QueryConditions {
  row?: number;
  column?: number;
  color?: string;
  level?: LevelType;
  group?: number;
  groupType?: GroupType;
}

/**
 * CellDataManager - 单元格数据管理器
 * 提供统一的数据访问接口和标准化的CRUD操作
 */
export class CellDataManager {
  private static instance: CellDataManager;
  private cellsMap: Map<string, CellData> = new Map();
  private indexMap: Map<number, string> = new Map(); // index -> id 映射
  private positionMap: Map<string, string> = new Map(); // "row,col" -> id 映射
  private groupMap: Map<number, Set<string>> = new Map(); // group -> cellIds 映射
  private colorMap: Map<string, Set<string>> = new Map(); // color -> cellIds 映射
  private levelMap: Map<LevelType, Set<string>> = new Map(); // level -> cellIds 映射
  private changeListeners: ((event: DataChangeEvent) => void)[] = [];

  private constructor() {
    this.initializeMaps();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): CellDataManager {
    if (!CellDataManager.instance) {
      CellDataManager.instance = new CellDataManager();
    }
    return CellDataManager.instance;
  }

  /**
   * 初始化索引映射
   */
  private initializeMaps(): void {
    // 初始化级别映射
    [1, 2, 3, 4].forEach(level => {
      this.levelMap.set(level as LevelType, new Set());
    });
  }

  /**
   * 创建单元格
   */
  public createCell(cellData: Omit<CellData, 'id'>): CellData {
    const id = this.generateCellId(cellData.row, cellData.column);
    const cell: CellData = {
      ...cellData,
      id,
      col: cellData.column, // 向后兼容
      number: cellData.index, // 向后兼容
    };

    this.cellsMap.set(id, cell);
    this.updateIndexes(cell);
    this.notifyChange({ type: 'create', cellIds: [id], timestamp: Date.now() });

    return cell;
  }

  /**
   * 获取单元格（通过ID）
   */
  public getCellById(id: string): CellData | undefined {
    return this.cellsMap.get(id);
  }

  /**
   * 获取单元格（通过位置）
   */
  public getCellByPosition(row: number, column: number): CellData | undefined {
    const id = this.positionMap.get(`${row},${column}`);
    return id ? this.cellsMap.get(id) : undefined;
  }

  /**
   * 获取单元格（通过索引）
   */
  public getCellByIndex(index: number): CellData | undefined {
    const id = this.indexMap.get(index);
    return id ? this.cellsMap.get(id) : undefined;
  }

  /**
   * 更新单元格
   */
  public updateCell(id: string, updates: Partial<CellData>): boolean {
    const cell = this.cellsMap.get(id);
    if (!cell) return false;

    // 移除旧的索引
    this.removeFromIndexes(cell);

    // 更新数据
    const updatedCell = { ...cell, ...updates };
    this.cellsMap.set(id, updatedCell);

    // 更新索引
    this.updateIndexes(updatedCell);
    this.notifyChange({ type: 'update', cellIds: [id], timestamp: Date.now() });

    return true;
  }

  /**
   * 删除单元格
   */
  public deleteCell(id: string): boolean {
    const cell = this.cellsMap.get(id);
    if (!cell) return false;

    this.removeFromIndexes(cell);
    this.cellsMap.delete(id);
    this.notifyChange({ type: 'delete', cellIds: [id], timestamp: Date.now() });

    return true;
  }

  /**
   * 批量更新单元格
   */
  public batchUpdate(updates: Array<{ id: string; data: Partial<CellData> }>): boolean {
    const updatedIds: string[] = [];

    updates.forEach(({ id, data }) => {
      if (this.updateCell(id, data)) {
        updatedIds.push(id);
      }
    });

    if (updatedIds.length > 0) {
      this.notifyChange({ type: 'batch', cellIds: updatedIds, timestamp: Date.now() });
    }

    return updatedIds.length === updates.length;
  }

  /**
   * 查询单元格
   */
  public queryCells(conditions: QueryConditions): CellData[] {
    let results: Set<string> = new Set(this.cellsMap.keys());

    // 按条件过滤
    if (conditions.row !== undefined || conditions.column !== undefined) {
      const positionKey = `${conditions.row ?? '*'},${conditions.column ?? '*'}`;
      if (conditions.row !== undefined && conditions.column !== undefined) {
        const id = this.positionMap.get(positionKey);
        results = id ? new Set([id]) : new Set();
      } else {
        // 部分位置匹配
        results = new Set(
          Array.from(this.positionMap.entries())
            .filter(([key]) => {
              const [r, c] = key.split(',').map(Number);
              return (conditions.row === undefined || r === conditions.row) &&
                     (conditions.column === undefined || c === conditions.column);
            })
            .map(([, id]) => id)
        );
      }
    }

    if (conditions.color) {
      const colorCells = this.colorMap.get(conditions.color) || new Set();
      results = new Set(Array.from(results).filter(id => colorCells.has(id)));
    }

    if (conditions.level) {
      const levelCells = this.levelMap.get(conditions.level) || new Set();
      results = new Set(Array.from(results).filter(id => levelCells.has(id)));
    }

    if (conditions.group !== undefined) {
      const groupCells = this.groupMap.get(conditions.group) || new Set();
      results = new Set(Array.from(results).filter(id => groupCells.has(id)));
    }

    if (conditions.groupType) {
      const groupRange = conditions.groupType === 'cross' ? [1, 10] : [11, 44];
      results = new Set(Array.from(results).filter(id => {
        const cell = this.cellsMap.get(id);
        return cell && cell.group >= groupRange[0] && cell.group <= groupRange[1];
      }));
    }

    return Array.from(results).map(id => this.cellsMap.get(id)!).filter(Boolean);
  }

  /**
   * 获取所有单元格
   */
  public getAllCells(): CellData[] {
    return Array.from(this.cellsMap.values());
  }

  /**
   * 获取分组中的所有单元格
   */
  public getCellsByGroup(group: number): CellData[] {
    const cellIds = this.groupMap.get(group) || new Set();
    return Array.from(cellIds).map(id => this.cellsMap.get(id)!).filter(Boolean);
  }

  /**
   * 获取指定颜色的所有单元格
   */
  public getCellsByColor(color: string): CellData[] {
    const cellIds = this.colorMap.get(color) || new Set();
    return Array.from(cellIds).map(id => this.cellsMap.get(id)!).filter(Boolean);
  }

  /**
   * 获取指定级别的所有单元格
   */
  public getCellsByLevel(level: LevelType): CellData[] {
    const cellIds = this.levelMap.get(level) || new Set();
    return Array.from(cellIds).map(id => this.cellsMap.get(id)!).filter(Boolean);
  }

  /**
   * 生成单元格ID
   */
  private generateCellId(row: number, column: number): string {
    return `cell_${row}_${column}`;
  }

  /**
   * 更新索引映射
   */
  private updateIndexes(cell: CellData): void {
    // 更新位置映射
    this.positionMap.set(`${cell.row},${cell.column}`, cell.id);
    
    // 更新索引映射
    this.indexMap.set(cell.index, cell.id);
    
    // 更新分组映射
    if (!this.groupMap.has(cell.group)) {
      this.groupMap.set(cell.group, new Set());
    }
    this.groupMap.get(cell.group)!.add(cell.id);
    
    // 更新颜色映射
    if (!this.colorMap.has(cell.color)) {
      this.colorMap.set(cell.color, new Set());
    }
    this.colorMap.get(cell.color)!.add(cell.id);
    
    // 更新级别映射
    if (this.levelMap.has(cell.level as LevelType)) {
      this.levelMap.get(cell.level as LevelType)!.add(cell.id);
    }
  }

  /**
   * 从索引中移除单元格
   */
  private removeFromIndexes(cell: CellData): void {
    this.positionMap.delete(`${cell.row},${cell.column}`);
    this.indexMap.delete(cell.index);
    
    this.groupMap.get(cell.group)?.delete(cell.id);
    this.colorMap.get(cell.color)?.delete(cell.id);
    this.levelMap.get(cell.level as LevelType)?.delete(cell.id);
  }

  /**
   * 添加数据变更监听器
   */
  public addChangeListener(listener: (event: DataChangeEvent) => void): void {
    this.changeListeners.push(listener);
  }

  /**
   * 移除数据变更监听器
   */
  public removeChangeListener(listener: (event: DataChangeEvent) => void): void {
    const index = this.changeListeners.indexOf(listener);
    if (index > -1) {
      this.changeListeners.splice(index, 1);
    }
  }

  /**
   * 通知数据变更
   */
  private notifyChange(event: DataChangeEvent): void {
    this.changeListeners.forEach(listener => listener(event));
  }

  /**
   * 清空所有数据
   */
  public clear(): void {
    this.cellsMap.clear();
    this.indexMap.clear();
    this.positionMap.clear();
    this.groupMap.clear();
    this.colorMap.clear();
    this.levelMap.clear();
    this.initializeMaps();
    this.notifyChange({ type: 'batch', cellIds: [], timestamp: Date.now() });
  }

  /**
   * 获取统计信息
   */
  public getStats() {
    return {
      totalCells: this.cellsMap.size,
      groupCount: this.groupMap.size,
      colorCount: this.colorMap.size,
      levelDistribution: Object.fromEntries(
        Array.from(this.levelMap.entries()).map(([level, cells]) => [level, cells.size])
      ),
    };
  }
}
