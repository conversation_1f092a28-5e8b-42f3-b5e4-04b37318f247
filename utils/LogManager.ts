/**
 * LogManager - 统一日志管理系统
 * 🎯 核心价值：解决控制台spam问题，提供精确的调试控制
 * ⚡ 性能优化：分级日志、坐标过滤、模块分类
 * 📊 功能范围：全局开关、级别控制、精简模式、调试过滤
 * 🔄 架构设计：单例模式、兼容现有sessionStorage机制
 */

// 日志级别枚举
export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3
}

// 日志模块类型
export type LogModule = 'color' | 'group' | 'cell' | 'render' | 'data' | 'performance' | 'general';

// 日志配置接口
export interface LogConfig {
  enabled: boolean;
  level: LogLevel;
  compactMode: boolean;
  coordinateFilter: string | null;
  moduleFilter: LogModule[] | null;
  showTimestamp: boolean;
}

// 日志条目接口
export interface LogEntry {
  timestamp: number;
  level: LogLevel;
  module: LogModule;
  message: string;
  data?: any;
  coordinate?: string;
}

/**
 * LogManager 单例类
 */
export class LogManager {
  private static instance: LogManager;
  private config: LogConfig;
  private logHistory: LogEntry[] = [];
  private maxHistorySize = 1000;

  private constructor() {
    // 初始化配置，兼容现有sessionStorage机制
    this.config = {
      enabled: true,
      level: LogLevel.WARN, // 默认精简模式，只显示警告和错误
      compactMode: this.getSessionStorageBoolean('debug-compact-mode', true),
      coordinateFilter: this.getSessionStorageString('debug-coords-filter'),
      moduleFilter: null,
      showTimestamp: false,
    };

    // 监听sessionStorage变化以保持兼容性
    this.syncWithSessionStorage();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): LogManager {
    if (!LogManager.instance) {
      LogManager.instance = new LogManager();
    }
    return LogManager.instance;
  }

  /**
   * 同步sessionStorage设置
   */
  private syncWithSessionStorage(): void {
    if (typeof window !== 'undefined') {
      // 监听sessionStorage变化
      const compactMode = this.getSessionStorageBoolean('debug-compact-mode', true);
      const coordFilter = this.getSessionStorageString('debug-coords-filter');
      
      this.config.compactMode = compactMode;
      this.config.coordinateFilter = coordFilter;
      
      // 根据精简模式调整日志级别
      if (compactMode) {
        this.config.level = LogLevel.WARN;
      }
    }
  }

  /**
   * 获取sessionStorage布尔值
   */
  private getSessionStorageBoolean(key: string, defaultValue: boolean = false): boolean {
    if (typeof window === 'undefined') return defaultValue;
    const value = sessionStorage.getItem(key);
    return value === 'true';
  }

  /**
   * 获取sessionStorage字符串值
   */
  private getSessionStorageString(key: string): string | null {
    if (typeof window === 'undefined') return null;
    return sessionStorage.getItem(key);
  }

  /**
   * 设置sessionStorage值
   */
  private setSessionStorageItem(key: string, value: string): void {
    if (typeof window !== 'undefined') {
      sessionStorage.setItem(key, value);
    }
  }

  /**
   * 启用精简模式
   */
  public enableCompactMode(): void {
    this.config.compactMode = true;
    this.config.level = LogLevel.WARN;
    this.setSessionStorageItem('debug-compact-mode', 'true');
    this.info('general', '已启用精简模式，只显示警告和错误');
  }

  /**
   * 禁用精简模式
   */
  public disableCompactMode(): void {
    this.config.compactMode = false;
    this.config.level = LogLevel.DEBUG;
    this.setSessionStorageItem('debug-compact-mode', 'false');
    this.info('general', '已禁用精简模式，显示所有调试信息');
  }

  /**
   * 设置坐标过滤器
   */
  public setCoordinateFilter(coordinate: string | null): void {
    this.config.coordinateFilter = coordinate;
    if (coordinate) {
      this.setSessionStorageItem('debug-coords-filter', coordinate);
      this.info('general', `已设置坐标过滤器: ${coordinate}`);
    } else {
      if (typeof window !== 'undefined') {
        sessionStorage.removeItem('debug-coords-filter');
      }
      this.info('general', '已清除坐标过滤器');
    }
  }

  /**
   * 设置模块过滤器
   */
  public setModuleFilter(modules: LogModule[] | null): void {
    this.config.moduleFilter = modules;
    if (modules) {
      this.info('general', `已设置模块过滤器: ${modules.join(', ')}`);
    } else {
      this.info('general', '已清除模块过滤器');
    }
  }

  /**
   * 设置日志级别
   */
  public setLogLevel(level: LogLevel): void {
    this.config.level = level;
    this.info('general', `日志级别已设置为: ${LogLevel[level]}`);
  }

  /**
   * 启用/禁用日志
   */
  public setEnabled(enabled: boolean): void {
    this.config.enabled = enabled;
    if (enabled) {
      this.info('general', '日志系统已启用');
    }
  }

  /**
   * 检查是否应该记录日志
   */
  private shouldLog(level: LogLevel, module: LogModule, coordinate?: string): boolean {
    if (!this.config.enabled) return false;
    if (level > this.config.level) return false;
    
    // 坐标过滤
    if (this.config.coordinateFilter && coordinate !== this.config.coordinateFilter) {
      return false;
    }
    
    // 模块过滤
    if (this.config.moduleFilter && !this.config.moduleFilter.includes(module)) {
      return false;
    }
    
    return true;
  }

  /**
   * 格式化日志消息
   */
  private formatMessage(level: LogLevel, module: LogModule, message: string, coordinate?: string): string {
    const levelIcon = {
      [LogLevel.ERROR]: '❌',
      [LogLevel.WARN]: '⚠️',
      [LogLevel.INFO]: 'ℹ️',
      [LogLevel.DEBUG]: '🔍'
    };
    
    const moduleIcon = {
      color: '🎨',
      group: '👥',
      cell: '📱',
      render: '🖼️',
      data: '📊',
      performance: '⚡',
      general: '🔧'
    };
    
    let formattedMessage = `${levelIcon[level]} ${moduleIcon[module]}`;
    
    if (coordinate) {
      formattedMessage += ` [${coordinate}]`;
    }
    
    if (this.config.showTimestamp) {
      const timestamp = new Date().toLocaleTimeString();
      formattedMessage += ` [${timestamp}]`;
    }
    
    formattedMessage += ` ${message}`;
    
    return formattedMessage;
  }

  /**
   * 记录日志条目
   */
  private log(level: LogLevel, module: LogModule, message: string, data?: any, coordinate?: string): void {
    if (!this.shouldLog(level, module, coordinate)) return;
    
    const entry: LogEntry = {
      timestamp: Date.now(),
      level,
      module,
      message,
      data,
      coordinate
    };
    
    // 添加到历史记录
    this.logHistory.push(entry);
    if (this.logHistory.length > this.maxHistorySize) {
      this.logHistory.shift();
    }
    
    // 输出到控制台
    const formattedMessage = this.formatMessage(level, module, message, coordinate);
    
    switch (level) {
      case LogLevel.ERROR:
        console.error(formattedMessage, data || '');
        break;
      case LogLevel.WARN:
        console.warn(formattedMessage, data || '');
        break;
      case LogLevel.INFO:
        console.info(formattedMessage, data || '');
        break;
      case LogLevel.DEBUG:
        console.log(formattedMessage, data || '');
        break;
    }
  }

  /**
   * 错误日志
   */
  public error(module: LogModule, message: string, data?: any, coordinate?: string): void {
    this.log(LogLevel.ERROR, module, message, data, coordinate);
  }

  /**
   * 警告日志
   */
  public warn(module: LogModule, message: string, data?: any, coordinate?: string): void {
    this.log(LogLevel.WARN, module, message, data, coordinate);
  }

  /**
   * 信息日志
   */
  public info(module: LogModule, message: string, data?: any, coordinate?: string): void {
    this.log(LogLevel.INFO, module, message, data, coordinate);
  }

  /**
   * 调试日志
   */
  public debug(module: LogModule, message: string, data?: any, coordinate?: string): void {
    this.log(LogLevel.DEBUG, module, message, data, coordinate);
  }

  /**
   * 获取配置
   */
  public getConfig(): LogConfig {
    return { ...this.config };
  }

  /**
   * 获取日志历史
   */
  public getHistory(): LogEntry[] {
    return [...this.logHistory];
  }

  /**
   * 清除日志历史
   */
  public clearHistory(): void {
    this.logHistory = [];
    this.info('general', '日志历史已清除');
  }

  /**
   * 获取统计信息
   */
  public getStats(): { total: number; byLevel: Record<string, number>; byModule: Record<string, number> } {
    const stats = {
      total: this.logHistory.length,
      byLevel: {} as Record<string, number>,
      byModule: {} as Record<string, number>
    };
    
    this.logHistory.forEach(entry => {
      const levelName = LogLevel[entry.level];
      stats.byLevel[levelName] = (stats.byLevel[levelName] || 0) + 1;
      stats.byModule[entry.module] = (stats.byModule[entry.module] || 0) + 1;
    });
    
    return stats;
  }
}

// 导出单例实例
export const logger = LogManager.getInstance();

// 全局访问接口
if (typeof window !== 'undefined') {
  (window as any).logger = logger;
  (window as any).LogManager = LogManager;
  (window as any).LogLevel = LogLevel;
}
