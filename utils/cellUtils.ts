import { type CellData } from '@/types/grid';

/**
 * 生成网格数据
 */
export const generateGridData = (): CellData[] => {
  const cells: CellData[] = [];
  let idCounter = 0;

  const GRID_DIMENSIONS = { ROWS: 15, COLS: 15 };
  const GRID_CENTER = { X: 7, Y: 7 };

  for (let row = 0; row < GRID_DIMENSIONS.ROWS; row++) {
    for (let col = 0; col < GRID_DIMENSIONS.COLS; col++) {
      const cellNumber = calculateCellNumber(row, col);
      cells.push({
        id: `cell-${idCounter}`,
        index: idCounter,
        number: cellNumber,
        color: '#000000',
        colorMappingValue: 0,
        level: 1,
        group: 0,
        row,
        column: col,
        col, // 向后兼容
        x: col - GRID_CENTER.X, // 中心为原点
        y: GRID_CENTER.Y - row, // Y轴向上为正
      });
      idCounter++;
    }
  }

  return cells;
};

/**
 * 计算单元格的8进制数值
 */
export const calculateCellNumber = (row: number, col: number): number => {
  const GRID_DIMENSIONS = { ROWS: 15, COLS: 15 };
  return (row * GRID_DIMENSIONS.COLS + col);
};

/**
 * 坐标转换函数
 */
export const gridToScreenCoordinates = (
  x: number, 
  y: number, 
  cellSize: number
): [number, number] => {
  const GRID_CENTER = { X: 7, Y: 7 };
  const screenX = (x + GRID_CENTER.X) * cellSize;
  const screenY = (GRID_CENTER.Y - y) * cellSize;
  return [screenX, screenY];
};

/**
 * 检查单元格是否在网格边界内
 */
export const isValidCoordinate = (x: number, y: number): boolean => {
  const GRID_DIMENSIONS = { ROWS: 15, COLS: 15 };
  const maxOffset = Math.floor(GRID_DIMENSIONS.ROWS / 2);
  return x >= -maxOffset && x <= maxOffset && y >= -maxOffset && y <= maxOffset;
}; 