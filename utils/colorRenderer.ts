/**
 * 颜色渲染系统 - 重构版
 * 🎯 核心价值：基于统一CellData结构的颜色计算和渲染
 * ⚡ 性能优化：缓存机制、批量计算、memoization
 * 📊 功能范围：颜色计算、样式生成、级别管理、分组渲染
 * 🔄 架构设计：基于CellDataManager的统一数据访问
 */

import type { CellData } from '../types/grid';
import { CellDataManager, type ColorType, type LevelType } from './CellDataManager';
import { COLOR_HEX_MAP, LEVEL_AVAILABILITY, getColorTypeFromHex } from './cellDataHelpers';

// 颜色渲染配置
export interface ColorRenderConfig {
  showCells: boolean;
  showLevel1: boolean;
  showLevel2: boolean;
  showLevel3: boolean;
  showLevel4: boolean;
  opacity: number;
  brightness: number;
}

// 样式计算结果
export interface CellStyle {
  backgroundColor: string;
  color: string;
  opacity: number;
  border: string;
  borderRadius: string;
  transform: string;
  visibility: 'visible' | 'hidden';
  zIndex: number;
}

// 颜色渲染器类
export class ColorRenderer {
  private static instance: ColorRenderer;
  private manager: CellDataManager;
  private styleCache: Map<string, CellStyle> = new Map();
  private configCache: Map<ColorType, ColorRenderConfig> = new Map();
  private readonly cacheLimit = 2000;

  private constructor() {
    this.manager = CellDataManager.getInstance();
    this.initializeDefaultConfigs();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): ColorRenderer {
    if (!ColorRenderer.instance) {
      ColorRenderer.instance = new ColorRenderer();
    }
    return ColorRenderer.instance;
  }

  /**
   * 初始化默认配置
   */
  private initializeDefaultConfigs(): void {
    const defaultConfig: ColorRenderConfig = {
      showCells: true,
      showLevel1: true,
      showLevel2: true,
      showLevel3: true,
      showLevel4: true,
      opacity: 1.0,
      brightness: 1.0,
    };

    Object.keys(COLOR_HEX_MAP).forEach(color => {
      this.configCache.set(color as ColorType, { ...defaultConfig });
    });
  }

  /**
   * 设置颜色渲染配置
   */
  public setColorConfig(color: ColorType, config: Partial<ColorRenderConfig>): void {
    const currentConfig = this.configCache.get(color) || this.getDefaultConfig();
    this.configCache.set(color, { ...currentConfig, ...config });
    this.clearStyleCache();
  }

  /**
   * 获取颜色渲染配置
   */
  public getColorConfig(color: ColorType): ColorRenderConfig {
    return this.configCache.get(color) || this.getDefaultConfig();
  }

  /**
   * 计算单元格样式
   */
  public calculateCellStyle(
    cell: CellData,
    options: {
      cellShape?: 'square' | 'rounded' | 'circle';
      fontSize?: number;
      enableHover?: boolean;
      isSelected?: boolean;
    } = {}
  ): CellStyle {
    const {
      cellShape = 'square',
      fontSize = 12,
      enableHover = false,
      isSelected = false,
    } = options;

    // 生成缓存键
    const cacheKey = this.generateStyleCacheKey(cell, options);
    
    // 检查缓存
    if (this.styleCache.has(cacheKey)) {
      return this.styleCache.get(cacheKey)!;
    }

    // 计算样式
    const style = this.computeCellStyle(cell, {
      cellShape,
      fontSize,
      enableHover,
      isSelected,
    });

    // 缓存结果
    this.setCachedStyle(cacheKey, style);

    return style;
  }

  /**
   * 计算单元格内容
   */
  public calculateCellContent(
    cell: CellData,
    displayMode: 'number' | 'coordinate' | 'hidden' = 'number'
  ): string {
    switch (displayMode) {
      case 'number':
        return cell.index.toString();
      case 'coordinate':
        return `${cell.row},${cell.column}`;
      case 'hidden':
        return '';
      default:
        return cell.index.toString();
    }
  }

  /**
   * 批量计算多个单元格样式
   */
  public batchCalculateStyles(
    cells: CellData[],
    options: Parameters<ColorRenderer['calculateCellStyle']>[1] = {}
  ): Map<string, CellStyle> {
    const results = new Map<string, CellStyle>();

    cells.forEach(cell => {
      const style = this.calculateCellStyle(cell, options);
      results.set(cell.id, style);
    });

    return results;
  }

  /**
   * 获取有效颜色（考虑优先级和可见性）
   */
  public getEffectiveColor(cell: CellData): string {
    const colorType = getColorTypeFromHex(cell.color);
    if (!colorType) return COLOR_HEX_MAP.black;

    const config = this.getColorConfig(colorType);

    // 检查颜色是否可见
    if (!config.showCells) return COLOR_HEX_MAP.black;

    // 检查级别是否可见
    const levelKey = `showLevel${cell.level}` as keyof ColorRenderConfig;
    if (!config[levelKey]) return COLOR_HEX_MAP.black;

    // 检查级别是否可用
    if (!LEVEL_AVAILABILITY[colorType]?.includes(cell.level as LevelType)) {
      return COLOR_HEX_MAP.black;
    }

    // 使用colorMappingValue进行颜色计算（如果需要）
    return this.calculateMappedColor(cell);
  }

  /**
   * 基于colorMappingValue计算映射颜色
   */
  public calculateMappedColor(cell: CellData): string {
    // 基于colorMappingValue调整颜色亮度或饱和度
    const colorType = getColorTypeFromHex(cell.color);
    if (!colorType) return cell.color;

    // 如果colorMappingValue未定义，使用默认值
    const mappingValue = cell.colorMappingValue ?? 0;

    // 将colorMappingValue映射到0.7-1.3范围的亮度调整
    const brightnessAdjustment = 0.7 + (mappingValue % 100) / 100 * 0.6;

    return this.getAdjustedColor(cell.color, brightnessAdjustment);
  }

  /**
   * 获取颜色亮度调整后的值
   */
  public getAdjustedColor(color: string, brightness: number = 1.0): string {
    if (brightness === 1.0) return color;

    // 解析十六进制颜色
    const hex = color.replace('#', '');
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);

    // 调整亮度
    const adjustedR = Math.min(255, Math.max(0, Math.round(r * brightness)));
    const adjustedG = Math.min(255, Math.max(0, Math.round(g * brightness)));
    const adjustedB = Math.min(255, Math.max(0, Math.round(b * brightness)));

    // 转换回十六进制
    return `#${adjustedR.toString(16).padStart(2, '0')}${adjustedG.toString(16).padStart(2, '0')}${adjustedB.toString(16).padStart(2, '0')}`;
  }

  /**
   * 计算实际样式
   */
  private computeCellStyle(
    cell: CellData,
    options: {
      cellShape: 'square' | 'rounded' | 'circle';
      fontSize: number;
      enableHover: boolean;
      isSelected: boolean;
    }
  ): CellStyle {
    const { cellShape, fontSize, enableHover, isSelected } = options;
    
    // 获取有效颜色
    const effectiveColor = this.getEffectiveColor(cell);
    const colorType = getColorTypeFromHex(effectiveColor);
    const config = colorType ? this.getColorConfig(colorType) : this.getDefaultConfig();

    // 调整颜色亮度
    const adjustedColor = this.getAdjustedColor(effectiveColor, config.brightness);

    // 计算文本颜色（基于背景色亮度）
    const textColor = this.calculateTextColor(adjustedColor);

    // 计算边框样式
    const borderStyle = this.calculateBorderStyle(cell, isSelected);

    // 计算形状样式
    const shapeStyle = this.calculateShapeStyle(cellShape);

    // 计算变换样式
    const transformStyle = this.calculateTransformStyle(cell, enableHover);

    return {
      backgroundColor: adjustedColor,
      color: textColor,
      opacity: config.opacity,
      border: borderStyle,
      borderRadius: shapeStyle.borderRadius,
      transform: transformStyle,
      visibility: config.showCells ? 'visible' : 'hidden',
      zIndex: this.calculateZIndex(cell),
    };
  }

  /**
   * 计算文本颜色
   */
  private calculateTextColor(backgroundColor: string): string {
    // 计算背景色亮度
    const hex = backgroundColor.replace('#', '');
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);

    // 使用相对亮度公式
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

    return luminance > 0.5 ? '#000000' : '#FFFFFF';
  }

  /**
   * 计算边框样式
   */
  private calculateBorderStyle(cell: CellData, isSelected: boolean): string {
    if (isSelected) {
      return '2px solid #007bff';
    }
    
    if (cell.group > 0) {
      return '1px solid rgba(0, 0, 0, 0.2)';
    }
    
    return '1px solid rgba(0, 0, 0, 0.1)';
  }

  /**
   * 计算形状样式
   */
  private calculateShapeStyle(cellShape: 'square' | 'rounded' | 'circle'): { borderRadius: string } {
    switch (cellShape) {
      case 'circle':
        return { borderRadius: '50%' };
      case 'rounded':
        return { borderRadius: '4px' };
      case 'square':
      default:
        return { borderRadius: '0' };
    }
  }

  /**
   * 计算变换样式
   */
  private calculateTransformStyle(cell: CellData, enableHover: boolean): string {
    const transforms: string[] = [];
    
    // 基于级别的缩放
    const scaleValue = 0.8 + (cell.level * 0.05);
    transforms.push(`scale(${scaleValue})`);
    
    // 基于分组的旋转
    if (cell.group > 0) {
      const rotation = (cell.group * 2) % 360;
      transforms.push(`rotate(${rotation}deg)`);
    }
    
    return transforms.join(' ');
  }

  /**
   * 计算Z-Index
   */
  private calculateZIndex(cell: CellData): number {
    // 基础层级
    let zIndex = 1;
    
    // 级别越高，层级越高
    zIndex += cell.level * 10;
    
    // 分组单元格层级更高
    if (cell.group > 0) {
      zIndex += 100;
    }
    
    return zIndex;
  }

  /**
   * 生成样式缓存键
   */
  private generateStyleCacheKey(
    cell: CellData,
    options: Parameters<ColorRenderer['calculateCellStyle']>[1]
  ): string {
    return `${cell.id}_${cell.color}_${cell.level}_${cell.group}_${JSON.stringify(options)}`;
  }

  /**
   * 设置缓存样式
   */
  private setCachedStyle(key: string, style: CellStyle): void {
    // 检查缓存限制
    if (this.styleCache.size >= this.cacheLimit) {
      // 删除最旧的缓存项
      const firstKey = this.styleCache.keys().next().value;
      if (firstKey !== undefined) {
        this.styleCache.delete(firstKey);
      }
    }
    
    this.styleCache.set(key, style);
  }

  /**
   * 清空样式缓存
   */
  public clearStyleCache(): void {
    this.styleCache.clear();
  }

  /**
   * 获取默认配置
   */
  private getDefaultConfig(): ColorRenderConfig {
    return {
      showCells: true,
      showLevel1: true,
      showLevel2: true,
      showLevel3: true,
      showLevel4: true,
      opacity: 1.0,
      brightness: 1.0,
    };
  }

  /**
   * 获取缓存统计信息
   */
  public getCacheStats() {
    return {
      styleCache: this.styleCache.size,
      configCache: this.configCache.size,
      cacheLimit: this.cacheLimit,
    };
  }
}
