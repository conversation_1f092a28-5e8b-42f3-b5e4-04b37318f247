# Cube1_Group 部署指南

## 🎯 概述

本指南详细说明如何将Cube1_Group项目部署到Vercel生产环境，包括数据库配置、环境变量设置和部署流程。

## 📋 部署前准备

### 1. 环境要求

- Node.js 18+
- npm 或 yarn
- Git
- Vercel CLI (可选)

### 2. 账户准备

- [Vercel账户](https://vercel.com)
- [GitHub账户](https://github.com) (用于代码托管)

## 🗄️ 数据库设置

### 选项1: Vercel PostgreSQL (推荐)

1. 在Vercel项目中添加PostgreSQL数据库:
   ```bash
   # 在Vercel Dashboard中
   Project Settings > Storage > Create Database > PostgreSQL
   ```

2. 获取数据库连接字符串:
   - 复制 `DATABASE_URL` 环境变量
   - 格式: `postgresql://username:password@host:port/database`

### 选项2: 外部PostgreSQL

支持的提供商:
- [Supabase](https://supabase.com)
- [PlanetScale](https://planetscale.com)
- [Railway](https://railway.app)
- [Neon](https://neon.tech)

## 🔧 环境变量配置

### 1. 复制模板文件

```bash
cp .env.production.template .env.production.local
```

### 2. 设置必需变量

编辑 `.env.production.local`:

```env
# 数据库连接
DATABASE_URL=postgresql://username:password@host:port/database

# 认证密钥 (生成随机字符串)
NEXTAUTH_SECRET=your-secure-random-secret

# 应用URL
NEXTAUTH_URL=https://your-domain.vercel.app
```

### 3. 生成安全密钥

```bash
# 生成NEXTAUTH_SECRET
openssl rand -base64 32

# 或使用在线工具
# https://generate-secret.vercel.app/32
```

## 🚀 部署流程

### 方法1: GitHub集成 (推荐)

1. **推送代码到GitHub**:
   ```bash
   git add .
   git commit -m "准备生产环境部署"
   git push origin main
   ```

2. **连接Vercel**:
   - 访问 [Vercel Dashboard](https://vercel.com/dashboard)
   - 点击 "New Project"
   - 选择GitHub仓库
   - 导入项目

3. **配置环境变量**:
   - 在项目设置中添加环境变量
   - 复制 `.env.production.template` 中的变量
   - 设置为 "Production" 环境

4. **部署**:
   - Vercel会自动构建和部署
   - 等待部署完成

### 方法2: Vercel CLI

1. **安装Vercel CLI**:
   ```bash
   npm install -g vercel
   ```

2. **登录Vercel**:
   ```bash
   vercel login
   ```

3. **配置生产环境**:
   ```bash
   npm run env:prod
   ```

4. **部署到生产环境**:
   ```bash
   npm run deploy:vercel
   ```

## 📊 数据库迁移

### 首次部署

部署后运行数据库迁移:

```bash
# 在Vercel Functions中自动运行
# 或手动运行:
npx prisma migrate deploy
npx prisma db seed
```

### 后续更新

数据库schema更新时:

```bash
# 1. 创建迁移
npx prisma migrate dev --name your-migration-name

# 2. 提交迁移文件
git add prisma/migrations/
git commit -m "添加数据库迁移"
git push

# 3. Vercel会自动应用迁移
```

## 🔍 部署验证

### 1. 健康检查

访问以下端点验证部署:

- 应用首页: `https://your-domain.vercel.app`
- API健康检查: `https://your-domain.vercel.app/api/health`
- 构建信息: `https://your-domain.vercel.app/build-info.json`

### 2. 功能测试

- [ ] 页面正常加载
- [ ] API响应正常
- [ ] 数据库连接正常
- [ ] 用户认证功能
- [ ] 数据持久化

### 3. 性能检查

使用工具检查性能:
- [Vercel Analytics](https://vercel.com/analytics)
- [Google PageSpeed Insights](https://pagespeed.web.dev/)
- [GTmetrix](https://gtmetrix.com/)

## 🛠️ 故障排除

### 常见问题

1. **数据库连接失败**:
   ```
   Error: P1001: Can't reach database server
   ```
   - 检查 `DATABASE_URL` 是否正确
   - 确认数据库服务器可访问
   - 验证网络连接

2. **环境变量未设置**:
   ```
   Error: Environment variable not found
   ```
   - 检查Vercel环境变量设置
   - 确认变量名拼写正确
   - 重新部署项目

3. **构建失败**:
   ```
   Error: Build failed
   ```
   - 检查构建日志
   - 验证依赖项版本
   - 运行本地构建测试

### 调试工具

1. **Vercel日志**:
   ```bash
   vercel logs your-deployment-url
   ```

2. **本地生产构建**:
   ```bash
   npm run build:prod
   npm start
   ```

3. **数据库调试**:
   ```bash
   npx prisma studio
   ```

## 📈 监控和维护

### 1. 设置监控

- 启用Vercel Analytics
- 配置错误追踪 (如Sentry)
- 设置正常运行时间监控

### 2. 定期维护

- 更新依赖项
- 监控性能指标
- 备份数据库
- 检查安全更新

### 3. 扩展配置

根据需要调整:
- 函数超时时间
- 内存限制
- 地域配置
- CDN设置

## 🔗 相关链接

- [Vercel文档](https://vercel.com/docs)
- [Next.js部署指南](https://nextjs.org/docs/deployment)
- [Prisma部署指南](https://www.prisma.io/docs/guides/deployment)
- [项目GitHub仓库](https://github.com/your-username/cube1-group)

## 📞 支持

如遇到部署问题:

1. 检查本文档的故障排除部分
2. 查看项目GitHub Issues
3. 联系开发团队

---

**注意**: 请确保在生产环境中使用安全的密钥和配置，不要在代码中硬编码敏感信息。
