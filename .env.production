# 环境配置 - PRODUCTION
# 🎯 自动生成，请勿手动编辑

# === 环境标识 ===
NODE_ENV=production
NEXT_PUBLIC_ENV=production

# === 数据库配置 ===
# PostgreSQL生产数据库
# DATABASE_URL 将由Vercel自动设置
# 本地测试PostgreSQL: DATABASE_URL="postgresql://username:password@localhost:5432/cube1_group?schema=public"

# === 应用配置 ===
NEXTAUTH_SECRET=CHANGE_THIS_IN_PRODUCTION
NEXTAUTH_URL=https://your-domain.vercel.app

# === 功能开关 ===
NEXT_PUBLIC_ENABLE_API=true
NEXT_PUBLIC_ENABLE_MIGRATION=false
NEXT_PUBLIC_ENABLE_DEV_TOOLS=false

# === 性能配置 ===
NEXT_PUBLIC_API_TIMEOUT=15000
NEXT_PUBLIC_SYNC_INTERVAL=60000
