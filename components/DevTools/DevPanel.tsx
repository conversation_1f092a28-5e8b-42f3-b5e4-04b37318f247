/**
 * 开发工具面板
 * 🎯 核心价值：提供开发和调试工具，测试API功能
 * 🔄 开发支持：API测试、数据迁移、状态检查
 * ⚡ 仅开发环境：生产环境自动隐藏
 */

'use client';

import React, { useState, useEffect } from 'react';
import { healthApi, migrationApi, userApi, projectApi } from '@/lib/api-client';
import { useHybridDataStore } from '@/hooks/useHybridDataStore';

interface DevPanelProps {
  isVisible: boolean;
  onToggle: () => void;
}

export const DevPanel: React.FC<DevPanelProps> = ({ isVisible, onToggle }) => {
  const [apiStatus, setApiStatus] = useState<'unknown' | 'healthy' | 'error'>('unknown');
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  
  const hybridStore = useHybridDataStore();

  // 检查API健康状态
  const checkApiHealth = async () => {
    try {
      const result = await healthApi.check();
      setApiStatus('healthy');
      addTestResult(`✅ API健康检查通过: ${result.status}`);
    } catch (error) {
      setApiStatus('error');
      addTestResult(`❌ API健康检查失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };

  // 添加测试结果
  const addTestResult = (message: string) => {
    setTestResults(prev => [...prev.slice(-9), `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  // 测试用户API
  const testUserApi = async () => {
    setIsLoading(true);
    try {
      // 创建测试用户
      const user = await userApi.create({
        email: `test-${Date.now()}@example.com`,
        name: '测试用户',
      });
      addTestResult(`✅ 创建用户成功: ${user.name}`);

      // 获取用户
      const fetchedUser = await userApi.getById(user.id);
      addTestResult(`✅ 获取用户成功: ${fetchedUser.name}`);

      // 更新用户
      const updatedUser = await userApi.update(user.id, {
        name: '更新后的测试用户',
      });
      addTestResult(`✅ 更新用户成功: ${updatedUser.name}`);

      // 删除用户
      await userApi.delete(user.id);
      addTestResult(`✅ 删除用户成功`);
    } catch (error) {
      addTestResult(`❌ 用户API测试失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
    setIsLoading(false);
  };

  // 测试项目API
  const testProjectApi = async () => {
    setIsLoading(true);
    try {
      // 先创建一个用户
      const user = await userApi.create({
        email: `project-test-${Date.now()}@example.com`,
        name: '项目测试用户',
      });

      // 创建项目
      const project = await projectApi.create({
        name: '测试项目',
        description: '这是一个测试项目',
        userId: user.id,
      });
      addTestResult(`✅ 创建项目成功: ${project.name}`);

      // 获取项目列表
      const projects = await projectApi.getAll(user.id);
      addTestResult(`✅ 获取项目列表成功: ${projects.projects.length} 个项目`);

      // 清理测试数据
      await projectApi.delete(project.id);
      await userApi.delete(user.id);
      addTestResult(`✅ 清理测试数据成功`);
    } catch (error) {
      addTestResult(`❌ 项目API测试失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
    setIsLoading(false);
  };

  // 测试数据迁移
  const testMigration = async () => {
    setIsLoading(true);
    try {
      // 检查迁移需求
      const needCheck = await migrationApi.checkNeed();
      addTestResult(`📋 迁移需求检查: ${needCheck.needsMigration ? '需要迁移' : '无需迁移'}`);

      if (needCheck.needsMigration) {
        // 创建测试用户用于迁移
        const user = await userApi.create({
          email: `migration-test-${Date.now()}@example.com`,
          name: '迁移测试用户',
        });

        // 执行迁移
        const migrationResult = await migrationApi.migrate({ userId: user.id });
        
        if (migrationResult.isCompleted) {
          addTestResult(`✅ 数据迁移成功: 迁移了 ${migrationResult.migratedStores.join(', ')}`);
        } else {
          addTestResult(`❌ 数据迁移失败: ${migrationResult.errors.join(', ')}`);
        }
      }
    } catch (error) {
      addTestResult(`❌ 迁移测试失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
    setIsLoading(false);
  };

  // 测试混合存储
  const testHybridStore = async () => {
    setIsLoading(true);
    try {
      addTestResult(`📊 混合存储状态: ${hybridStore.hybridState.currentMode}`);
      addTestResult(`🌐 在线状态: ${hybridStore.hybridState.isOnline ? '在线' : '离线'}`);
      addTestResult(`🔗 API可用: ${hybridStore.hybridState.isApiAvailable ? '可用' : '不可用'}`);
      
      if (hybridStore.hybridState.needsMigration) {
        addTestResult(`⚠️ 需要数据迁移`);
      }
      
      if (hybridStore.hybridState.syncErrors.length > 0) {
        addTestResult(`❌ 同步错误: ${hybridStore.hybridState.syncErrors.join(', ')}`);
      }
    } catch (error) {
      addTestResult(`❌ 混合存储测试失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
    setIsLoading(false);
  };

  // 清空测试结果
  const clearResults = () => {
    setTestResults([]);
  };

  // 初始化时检查API状态
  useEffect(() => {
    if (isVisible) {
      checkApiHealth();
    }
  }, [isVisible]);

  if (!isVisible) {
    return (
      <button
        onClick={onToggle}
        className="fixed bottom-4 right-4 bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg hover:bg-blue-700 transition-colors z-50"
      >
        🛠️ 开发工具
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 w-96 bg-white border border-gray-300 rounded-lg shadow-xl z-50 max-h-96 overflow-hidden flex flex-col">
      {/* 头部 */}
      <div className="bg-gray-100 px-4 py-2 border-b border-gray-200 flex justify-between items-center">
        <h3 className="font-semibold text-gray-800">🛠️ 开发工具</h3>
        <div className="flex items-center gap-2">
          <div className={`w-2 h-2 rounded-full ${
            apiStatus === 'healthy' ? 'bg-green-500' : 
            apiStatus === 'error' ? 'bg-red-500' : 'bg-gray-400'
          }`} />
          <button
            onClick={onToggle}
            className="text-gray-500 hover:text-gray-700"
          >
            ✕
          </button>
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="p-4 border-b border-gray-200">
        <div className="grid grid-cols-2 gap-2 mb-3">
          <button
            onClick={checkApiHealth}
            disabled={isLoading}
            className="px-3 py-1 text-sm bg-green-100 text-green-700 rounded hover:bg-green-200 disabled:opacity-50"
          >
            健康检查
          </button>
          <button
            onClick={testUserApi}
            disabled={isLoading}
            className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 disabled:opacity-50"
          >
            测试用户API
          </button>
          <button
            onClick={testProjectApi}
            disabled={isLoading}
            className="px-3 py-1 text-sm bg-purple-100 text-purple-700 rounded hover:bg-purple-200 disabled:opacity-50"
          >
            测试项目API
          </button>
          <button
            onClick={testMigration}
            disabled={isLoading}
            className="px-3 py-1 text-sm bg-orange-100 text-orange-700 rounded hover:bg-orange-200 disabled:opacity-50"
          >
            测试迁移
          </button>
        </div>
        <div className="grid grid-cols-2 gap-2">
          <button
            onClick={testHybridStore}
            disabled={isLoading}
            className="px-3 py-1 text-sm bg-indigo-100 text-indigo-700 rounded hover:bg-indigo-200 disabled:opacity-50"
          >
            混合存储状态
          </button>
          <button
            onClick={clearResults}
            className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
          >
            清空结果
          </button>
        </div>
      </div>

      {/* 测试结果 */}
      <div className="flex-1 overflow-y-auto p-4 bg-white">
        <h4 className="text-sm font-medium text-gray-800 mb-2">测试结果:</h4>
        <div className="space-y-1">
          {testResults.length === 0 ? (
            <p className="text-sm text-gray-500">暂无测试结果</p>
          ) : (
            testResults.map((result, index) => (
              <div key={index} className="text-xs font-mono bg-gray-50 text-gray-800 p-2 rounded border border-gray-200">
                {result}
              </div>
            ))
          )}
        </div>
      </div>

      {/* 加载状态 */}
      {isLoading && (
        <div className="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center rounded-lg">
          <div className="text-sm text-gray-800 font-medium">测试中...</div>
        </div>
      )}
    </div>
  );
};
