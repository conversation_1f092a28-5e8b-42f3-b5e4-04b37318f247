/**
 * 开发工具入口组件
 * 🎯 核心价值：统一开发工具入口，环境检测
 * 🔄 条件渲染：仅在开发环境显示
 * ⚡ 懒加载：避免影响生产环境性能
 */

'use client';

import React, { useState, useEffect } from 'react';
import { DevPanel } from './DevPanel';

export const DevTools: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [shouldRender, setShouldRender] = useState(false);

  // 检查是否应该显示开发工具
  useEffect(() => {
    const isDevelopment = process.env.NODE_ENV === 'development';
    const isDevToolsEnabled = process.env.NEXT_PUBLIC_ENABLE_DEV_TOOLS === 'true';
    
    setShouldRender(isDevelopment && isDevToolsEnabled);
  }, []);

  // 键盘快捷键：Ctrl+Shift+D 切换开发工具
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey && event.shiftKey && event.key === 'D') {
        event.preventDefault();
        setIsVisible(prev => !prev);
      }
    };

    if (shouldRender) {
      window.addEventListener('keydown', handleKeyDown);
      return () => window.removeEventListener('keydown', handleKeyDown);
    }
  }, [shouldRender]);

  // 生产环境不渲染
  if (!shouldRender) {
    return null;
  }

  return (
    <DevPanel 
      isVisible={isVisible} 
      onToggle={() => setIsVisible(prev => !prev)} 
    />
  );
};

export default DevTools;
