#!/usr/bin/env node

/**
 * 代码质量监控脚本
 * 🎯 功能：自动化代码质量检查、指标收集、报告生成
 * 📊 监控：文件大小、复杂度、重复率、测试覆盖率
 * 🚨 告警：质量指标超标时自动告警
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class CodeQualityMonitor {
  constructor() {
    this.projectRoot = process.cwd();
    this.thresholds = {
      fileSize: 500,        // 文件最大行数
      functionSize: 100,    // 函数最大行数
      complexity: 15,       // 最大圈复杂度
      duplication: 5,       // 最大重复率(%)
      coverage: 80,         // 最小测试覆盖率(%)
    };
    this.results = {
      files: [],
      functions: [],
      issues: [],
      metrics: {},
    };
  }

  /**
   * 执行完整的代码质量检查
   */
  async runFullCheck() {
    console.log('🔍 开始代码质量监控...\n');
    
    try {
      // 1. 文件大小检查
      await this.checkFileSize();
      
      // 2. 函数复杂度检查
      await this.checkFunctionComplexity();
      
      // 3. 代码重复检查
      await this.checkCodeDuplication();
      
      // 4. 测试覆盖率检查
      await this.checkTestCoverage();
      
      // 5. TypeScript类型检查
      await this.checkTypeScript();
      
      // 6. ESLint检查
      await this.checkESLint();
      
      // 7. 生成报告
      await this.generateReport();
      
      console.log('✅ 代码质量检查完成！');
      
    } catch (error) {
      console.error('❌ 代码质量检查失败:', error.message);
      process.exit(1);
    }
  }

  /**
   * 检查文件大小
   */
  async checkFileSize() {
    console.log('📏 检查文件大小...');
    
    const files = this.getSourceFiles();
    const oversizedFiles = [];
    
    for (const file of files) {
      const content = fs.readFileSync(file, 'utf8');
      const lines = content.split('\n').length;
      
      this.results.files.push({
        path: file,
        lines,
        size: fs.statSync(file).size,
      });
      
      if (lines > this.thresholds.fileSize) {
        oversizedFiles.push({ file, lines });
        this.results.issues.push({
          type: 'file-size',
          severity: 'warning',
          file,
          message: `文件过大: ${lines}行 (限制: ${this.thresholds.fileSize}行)`,
        });
      }
    }
    
    console.log(`   检查了 ${files.length} 个文件`);
    if (oversizedFiles.length > 0) {
      console.log(`   ⚠️  发现 ${oversizedFiles.length} 个过大文件`);
    }
  }

  /**
   * 检查函数复杂度
   */
  async checkFunctionComplexity() {
    console.log('🧮 检查函数复杂度...');
    
    const files = this.getSourceFiles();
    let totalFunctions = 0;
    let complexFunctions = 0;
    
    for (const file of files) {
      const content = fs.readFileSync(file, 'utf8');
      const functions = this.extractFunctions(content);
      
      for (const func of functions) {
        totalFunctions++;
        const complexity = this.calculateComplexity(func.body);
        const lines = func.body.split('\n').length;
        
        this.results.functions.push({
          file,
          name: func.name,
          lines,
          complexity,
        });
        
        if (complexity > this.thresholds.complexity) {
          complexFunctions++;
          this.results.issues.push({
            type: 'complexity',
            severity: 'error',
            file,
            function: func.name,
            message: `函数复杂度过高: ${complexity} (限制: ${this.thresholds.complexity})`,
          });
        }
        
        if (lines > this.thresholds.functionSize) {
          this.results.issues.push({
            type: 'function-size',
            severity: 'warning',
            file,
            function: func.name,
            message: `函数过长: ${lines}行 (限制: ${this.thresholds.functionSize}行)`,
          });
        }
      }
    }
    
    console.log(`   检查了 ${totalFunctions} 个函数`);
    if (complexFunctions > 0) {
      console.log(`   ⚠️  发现 ${complexFunctions} 个高复杂度函数`);
    }
  }

  /**
   * 检查代码重复
   */
  async checkCodeDuplication() {
    console.log('🔄 检查代码重复...');
    
    try {
      // 使用jscpd检查代码重复
      const result = execSync('npx jscpd --format json --silent', { 
        encoding: 'utf8',
        cwd: this.projectRoot 
      });
      
      const duplicationData = JSON.parse(result);
      const duplicationRate = duplicationData.statistics.total.percentage;
      
      this.results.metrics.duplication = duplicationRate;
      
      if (duplicationRate > this.thresholds.duplication) {
        this.results.issues.push({
          type: 'duplication',
          severity: 'warning',
          message: `代码重复率过高: ${duplicationRate}% (限制: ${this.thresholds.duplication}%)`,
        });
      }
      
      console.log(`   代码重复率: ${duplicationRate}%`);
      
    } catch (error) {
      console.log('   ⚠️  无法检查代码重复 (需要安装jscpd)');
    }
  }

  /**
   * 检查测试覆盖率
   */
  async checkTestCoverage() {
    console.log('🧪 检查测试覆盖率...');
    
    try {
      const result = execSync('pnpm run test:coverage -- --silent', {
        encoding: 'utf8',
        cwd: this.projectRoot
      });
      
      // 解析覆盖率报告
      const coverageMatch = result.match(/All files\s+\|\s+([\d.]+)/);
      const coverage = coverageMatch ? parseFloat(coverageMatch[1]) : 0;
      
      this.results.metrics.coverage = coverage;
      
      if (coverage < this.thresholds.coverage) {
        this.results.issues.push({
          type: 'coverage',
          severity: 'error',
          message: `测试覆盖率不足: ${coverage}% (要求: ${this.thresholds.coverage}%)`,
        });
      }
      
      console.log(`   测试覆盖率: ${coverage}%`);
      
    } catch (error) {
      console.log('   ⚠️  无法获取测试覆盖率');
    }
  }

  /**
   * 检查TypeScript类型
   */
  async checkTypeScript() {
    console.log('🔷 检查TypeScript类型...');
    
    try {
      execSync('npx tsc --noEmit', { 
        encoding: 'utf8',
        cwd: this.projectRoot 
      });
      console.log('   ✅ TypeScript类型检查通过');
    } catch (error) {
      this.results.issues.push({
        type: 'typescript',
        severity: 'error',
        message: 'TypeScript类型检查失败',
        details: error.stdout,
      });
      console.log('   ❌ TypeScript类型检查失败');
    }
  }

  /**
   * 检查ESLint
   */
  async checkESLint() {
    console.log('🔧 检查ESLint规则...');
    
    try {
      const result = execSync('npx eslint . --format json', { 
        encoding: 'utf8',
        cwd: this.projectRoot 
      });
      
      const eslintResults = JSON.parse(result);
      let errorCount = 0;
      let warningCount = 0;
      
      for (const file of eslintResults) {
        errorCount += file.errorCount;
        warningCount += file.warningCount;
        
        for (const message of file.messages) {
          this.results.issues.push({
            type: 'eslint',
            severity: message.severity === 2 ? 'error' : 'warning',
            file: file.filePath,
            line: message.line,
            message: `${message.ruleId}: ${message.message}`,
          });
        }
      }
      
      console.log(`   错误: ${errorCount}, 警告: ${warningCount}`);
      
    } catch (error) {
      console.log('   ⚠️  ESLint检查出现问题');
    }
  }

  /**
   * 生成质量报告
   */
  async generateReport() {
    console.log('📊 生成质量报告...');
    
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalFiles: this.results.files.length,
        totalFunctions: this.results.functions.length,
        totalIssues: this.results.issues.length,
        errorCount: this.results.issues.filter(i => i.severity === 'error').length,
        warningCount: this.results.issues.filter(i => i.severity === 'warning').length,
      },
      metrics: this.results.metrics,
      thresholds: this.thresholds,
      issues: this.results.issues,
      files: this.results.files.map(f => ({
        path: f.path.replace(this.projectRoot, ''),
        lines: f.lines,
        size: f.size,
      })),
      functions: this.results.functions.map(f => ({
        file: f.file.replace(this.projectRoot, ''),
        name: f.name,
        lines: f.lines,
        complexity: f.complexity,
      })),
    };
    
    // 保存JSON报告
    const reportPath = path.join(this.projectRoot, 'reports', 'code-quality.json');
    fs.mkdirSync(path.dirname(reportPath), { recursive: true });
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    // 生成Markdown报告
    const markdownReport = this.generateMarkdownReport(report);
    const markdownPath = path.join(this.projectRoot, 'reports', 'code-quality.md');
    fs.writeFileSync(markdownPath, markdownReport);
    
    console.log(`   报告已保存: ${reportPath}`);
    console.log(`   Markdown报告: ${markdownPath}`);
    
    // 输出摘要
    this.printSummary(report);
  }

  /**
   * 获取源文件列表
   */
  getSourceFiles() {
    const extensions = ['.ts', '.tsx', '.js', '.jsx'];
    const excludeDirs = ['node_modules', '.next', 'dist', 'build', 'coverage'];
    
    const files = [];
    
    const scanDir = (dir) => {
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !excludeDirs.includes(item)) {
          scanDir(fullPath);
        } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
          files.push(fullPath);
        }
      }
    };
    
    scanDir(this.projectRoot);
    return files;
  }

  /**
   * 提取函数信息
   */
  extractFunctions(content) {
    const functions = [];
    const functionRegex = /(?:function\s+(\w+)|const\s+(\w+)\s*=.*?=>|(\w+)\s*:\s*.*?=>)/g;
    
    let match;
    while ((match = functionRegex.exec(content)) !== null) {
      const name = match[1] || match[2] || match[3];
      if (name) {
        functions.push({
          name,
          body: content, // 简化实现，实际应该提取函数体
        });
      }
    }
    
    return functions;
  }

  /**
   * 计算圈复杂度
   */
  calculateComplexity(code) {
    const complexityKeywords = [
      'if', 'else', 'while', 'for', 'switch', 'case', 
      'catch', '&&', '||', '?', 'break', 'continue'
    ];
    
    let complexity = 1; // 基础复杂度
    
    for (const keyword of complexityKeywords) {
      const regex = new RegExp(`\\b${keyword}\\b`, 'g');
      const matches = code.match(regex);
      if (matches) {
        complexity += matches.length;
      }
    }
    
    return complexity;
  }

  /**
   * 生成Markdown报告
   */
  generateMarkdownReport(report) {
    return `# 代码质量报告

## 📊 概览

- **检查时间**: ${new Date(report.timestamp).toLocaleString()}
- **文件总数**: ${report.summary.totalFiles}
- **函数总数**: ${report.summary.totalFunctions}
- **问题总数**: ${report.summary.totalIssues}
- **错误数量**: ${report.summary.errorCount}
- **警告数量**: ${report.summary.warningCount}

## 📈 质量指标

| 指标 | 当前值 | 阈值 | 状态 |
|------|--------|------|------|
| 测试覆盖率 | ${report.metrics.coverage || 'N/A'}% | ${report.thresholds.coverage}% | ${(report.metrics.coverage || 0) >= report.thresholds.coverage ? '✅' : '❌'} |
| 代码重复率 | ${report.metrics.duplication || 'N/A'}% | ${report.thresholds.duplication}% | ${(report.metrics.duplication || 0) <= report.thresholds.duplication ? '✅' : '❌'} |

## 🚨 问题列表

${report.issues.map(issue => `- **${issue.severity.toUpperCase()}**: ${issue.message}`).join('\n')}

## 📁 文件统计

最大的文件:
${report.files.sort((a, b) => b.lines - a.lines).slice(0, 5).map(f => `- ${f.path}: ${f.lines}行`).join('\n')}

## 🧮 函数统计

最复杂的函数:
${report.functions.sort((a, b) => b.complexity - a.complexity).slice(0, 5).map(f => `- ${f.file}:${f.name}: 复杂度${f.complexity}`).join('\n')}
`;
  }

  /**
   * 打印摘要信息
   */
  printSummary(report) {
    console.log('\n📊 质量摘要:');
    console.log(`   文件: ${report.summary.totalFiles}`);
    console.log(`   函数: ${report.summary.totalFunctions}`);
    console.log(`   问题: ${report.summary.totalIssues} (${report.summary.errorCount}错误, ${report.summary.warningCount}警告)`);
    
    if (report.summary.errorCount > 0) {
      console.log('\n❌ 发现严重问题，需要立即修复！');
      process.exit(1);
    } else if (report.summary.warningCount > 0) {
      console.log('\n⚠️  发现警告，建议优化');
    } else {
      console.log('\n✅ 代码质量良好！');
    }
  }
}

// 执行监控
if (require.main === module) {
  const monitor = new CodeQualityMonitor();
  monitor.runFullCheck().catch(console.error);
}

module.exports = CodeQualityMonitor;
