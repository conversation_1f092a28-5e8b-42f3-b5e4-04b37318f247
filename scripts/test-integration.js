/**
 * 集成测试脚本
 * 🎯 核心价值：全面验证API和前端功能
 * 🔄 自动化测试：API端点、数据一致性、功能完整性
 * ⚡ 快速验证：部署前的完整性检查
 */

const { execSync } = require('child_process');
const fs = require('fs');

// 测试配置
const TEST_CONFIG = {
  baseUrl: process.env.TEST_BASE_URL || 'http://localhost:3001',
  timeout: 10000,
  retries: 3,
};

// 测试结果
const testResults = {
  passed: 0,
  failed: 0,
  errors: [],
  details: [],
};

// 工具函数
function log(message, type = 'info') {
  const timestamp = new Date().toLocaleTimeString();
  const prefix = {
    info: '📋',
    success: '✅',
    error: '❌',
    warning: '⚠️',
  }[type] || '📋';
  
  console.log(`${prefix} [${timestamp}] ${message}`);
}

function addResult(testName, success, details = '') {
  if (success) {
    testResults.passed++;
    log(`${testName} - 通过`, 'success');
  } else {
    testResults.failed++;
    testResults.errors.push(testName);
    log(`${testName} - 失败: ${details}`, 'error');
  }
  
  testResults.details.push({
    name: testName,
    success,
    details,
    timestamp: new Date().toISOString(),
  });
}

// HTTP请求函数
async function makeRequest(url, options = {}) {
  const fetch = (await import('node-fetch')).default;
  
  const defaultOptions = {
    timeout: TEST_CONFIG.timeout,
    headers: {
      'Content-Type': 'application/json',
    },
  };
  
  const finalOptions = { ...defaultOptions, ...options };
  
  try {
    const response = await fetch(url, finalOptions);
    const data = await response.json();
    
    return {
      success: response.ok,
      status: response.status,
      data,
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
    };
  }
}

// 测试API健康检查
async function testApiHealth() {
  log('测试API健康检查...');
  
  const result = await makeRequest(`${TEST_CONFIG.baseUrl}/api/health`);
  
  if (result.success && result.data.success) {
    addResult('API健康检查', true);
    return true;
  } else {
    addResult('API健康检查', false, result.error || 'API响应异常');
    return false;
  }
}

// 测试用户API
async function testUserApi() {
  log('测试用户API...');
  
  try {
    // 创建用户
    const createResult = await makeRequest(`${TEST_CONFIG.baseUrl}/api/users`, {
      method: 'POST',
      body: JSON.stringify({
        email: `test-${Date.now()}@example.com`,
        name: '集成测试用户',
      }),
    });
    
    if (!createResult.success || !createResult.data.success) {
      addResult('用户API - 创建', false, '创建用户失败');
      return false;
    }
    
    const userId = createResult.data.data.id;
    addResult('用户API - 创建', true);
    
    // 获取用户
    const getResult = await makeRequest(`${TEST_CONFIG.baseUrl}/api/users/${userId}`);
    
    if (!getResult.success || !getResult.data.success) {
      addResult('用户API - 获取', false, '获取用户失败');
      return false;
    }
    
    addResult('用户API - 获取', true);
    
    // 更新用户
    const updateResult = await makeRequest(`${TEST_CONFIG.baseUrl}/api/users/${userId}`, {
      method: 'PUT',
      body: JSON.stringify({
        name: '更新后的测试用户',
      }),
    });
    
    if (!updateResult.success || !updateResult.data.success) {
      addResult('用户API - 更新', false, '更新用户失败');
      return false;
    }
    
    addResult('用户API - 更新', true);
    
    // 删除用户
    const deleteResult = await makeRequest(`${TEST_CONFIG.baseUrl}/api/users/${userId}`, {
      method: 'DELETE',
    });
    
    if (!deleteResult.success || !deleteResult.data.success) {
      addResult('用户API - 删除', false, '删除用户失败');
      return false;
    }
    
    addResult('用户API - 删除', true);
    return true;
    
  } catch (error) {
    addResult('用户API', false, error.message);
    return false;
  }
}

// 测试项目API
async function testProjectApi() {
  log('测试项目API...');
  
  try {
    // 先创建一个用户
    const userResult = await makeRequest(`${TEST_CONFIG.baseUrl}/api/users`, {
      method: 'POST',
      body: JSON.stringify({
        email: `project-test-${Date.now()}@example.com`,
        name: '项目测试用户',
      }),
    });
    
    if (!userResult.success) {
      addResult('项目API - 前置用户创建', false, '创建测试用户失败');
      return false;
    }
    
    const userId = userResult.data.data.id;
    
    // 创建项目
    const createResult = await makeRequest(`${TEST_CONFIG.baseUrl}/api/projects`, {
      method: 'POST',
      body: JSON.stringify({
        name: '集成测试项目',
        description: '自动化测试创建的项目',
        userId,
      }),
    });
    
    if (!createResult.success || !createResult.data.success) {
      addResult('项目API - 创建', false, '创建项目失败');
      return false;
    }
    
    addResult('项目API - 创建', true);
    
    // 获取项目列表
    const listResult = await makeRequest(`${TEST_CONFIG.baseUrl}/api/projects?userId=${userId}`);
    
    if (!listResult.success || !listResult.data.success) {
      addResult('项目API - 列表', false, '获取项目列表失败');
      return false;
    }
    
    addResult('项目API - 列表', true);
    
    // 清理测试数据
    await makeRequest(`${TEST_CONFIG.baseUrl}/api/users/${userId}`, {
      method: 'DELETE',
    });
    
    return true;
    
  } catch (error) {
    addResult('项目API', false, error.message);
    return false;
  }
}

// 测试数据迁移API
async function testMigrationApi() {
  log('测试数据迁移API...');
  
  try {
    const result = await makeRequest(`${TEST_CONFIG.baseUrl}/api/migration`);
    
    if (result.success && result.data.success) {
      addResult('数据迁移API', true);
      return true;
    } else {
      addResult('数据迁移API', false, result.error || '迁移API响应异常');
      return false;
    }
  } catch (error) {
    addResult('数据迁移API', false, error.message);
    return false;
  }
}

// 测试数据库连接
async function testDatabaseConnection() {
  log('测试数据库连接...');
  
  try {
    // 使用Prisma检查数据库连接
    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();
    
    await prisma.$connect();
    const userCount = await prisma.user.count();
    await prisma.$disconnect();
    
    addResult('数据库连接', true, `用户数量: ${userCount}`);
    return true;
  } catch (error) {
    addResult('数据库连接', false, error.message);
    return false;
  }
}

// 测试前端页面
async function testFrontendPages() {
  log('测试前端页面...');
  
  try {
    const result = await makeRequest(TEST_CONFIG.baseUrl);
    
    if (result.success) {
      addResult('前端页面', true);
      return true;
    } else {
      addResult('前端页面', false, '页面无法访问');
      return false;
    }
  } catch (error) {
    addResult('前端页面', false, error.message);
    return false;
  }
}

// 生成测试报告
function generateTestReport() {
  const total = testResults.passed + testResults.failed;
  const successRate = total > 0 ? ((testResults.passed / total) * 100).toFixed(1) : 0;
  
  const report = {
    summary: {
      total,
      passed: testResults.passed,
      failed: testResults.failed,
      successRate: `${successRate}%`,
      timestamp: new Date().toISOString(),
    },
    errors: testResults.errors,
    details: testResults.details,
  };
  
  // 写入报告文件
  fs.writeFileSync('./test-report.json', JSON.stringify(report, null, 2));
  
  return report;
}

// 显示测试结果
function showTestResults(report) {
  console.log('\n' + '='.repeat(50));
  console.log('🧪 集成测试结果');
  console.log('='.repeat(50));
  
  console.log(`📊 总测试数: ${report.summary.total}`);
  console.log(`✅ 通过: ${report.summary.passed}`);
  console.log(`❌ 失败: ${report.summary.failed}`);
  console.log(`📈 成功率: ${report.summary.successRate}`);
  
  if (report.errors.length > 0) {
    console.log('\n❌ 失败的测试:');
    report.errors.forEach(error => console.log(`   - ${error}`));
  }
  
  console.log(`\n📄 详细报告: ./test-report.json`);
  console.log('='.repeat(50));
}

// 主测试函数
async function runIntegrationTests() {
  console.log('🚀 开始集成测试...\n');
  
  try {
    // 检查依赖
    try {
      await import('node-fetch');
    } catch (error) {
      log('安装node-fetch依赖...', 'warning');
      execSync('pnpm install node-fetch@2', { stdio: 'inherit' });
    }
    
    // 运行测试
    await testApiHealth();
    await testDatabaseConnection();
    await testUserApi();
    await testProjectApi();
    await testMigrationApi();
    await testFrontendPages();
    
    // 生成报告
    const report = generateTestReport();
    showTestResults(report);
    
    // 返回结果
    return report.summary.failed === 0;
    
  } catch (error) {
    log(`集成测试失败: ${error.message}`, 'error');
    return false;
  }
}

// 运行测试
if (require.main === module) {
  runIntegrationTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('测试运行失败:', error);
      process.exit(1);
    });
}

module.exports = { runIntegrationTests };
