/**
 * 开发环境设置脚本
 * 🎯 核心价值：自动化开发环境初始化
 * 🔄 一键设置：数据库、种子数据、环境检查
 * ⚡ 开发友好：新开发者快速上手
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始设置 Cube1_Group 开发环境...\n');

// 检查必要文件
function checkRequiredFiles() {
  console.log('📋 检查必要文件...');
  
  const requiredFiles = [
    '.env',
    '.env.local',
    'prisma/schema.prisma',
    'prisma/seed.ts',
  ];
  
  const missingFiles = requiredFiles.filter(file => !fs.existsSync(file));
  
  if (missingFiles.length > 0) {
    console.error('❌ 缺少必要文件:');
    missingFiles.forEach(file => console.error(`   - ${file}`));
    process.exit(1);
  }
  
  console.log('✅ 所有必要文件存在\n');
}

// 检查数据库文件
function checkDatabase() {
  console.log('🗄️ 检查数据库...');
  
  const dbPath = './dev.db';
  const dbExists = fs.existsSync(dbPath);
  
  if (dbExists) {
    console.log('✅ 数据库文件已存在');
  } else {
    console.log('📝 数据库文件不存在，将创建新数据库');
  }
  
  return dbExists;
}

// 运行命令并处理错误
function runCommand(command, description) {
  console.log(`🔧 ${description}...`);
  try {
    execSync(command, { stdio: 'inherit' });
    console.log(`✅ ${description}完成\n`);
  } catch (error) {
    console.error(`❌ ${description}失败:`, error.message);
    process.exit(1);
  }
}

// 检查Node.js版本
function checkNodeVersion() {
  console.log('🔍 检查Node.js版本...');
  
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  
  if (majorVersion < 18) {
    console.error(`❌ Node.js版本过低: ${nodeVersion}`);
    console.error('   需要Node.js 18或更高版本');
    process.exit(1);
  }
  
  console.log(`✅ Node.js版本: ${nodeVersion}\n`);
}

// 检查包管理器
function checkPackageManager() {
  console.log('📦 检查包管理器...');

  try {
    execSync('pnpm --version', { stdio: 'pipe' });
    console.log('✅ pnpm 可用\n');
    return 'pnpm';
  } catch (error) {
    console.log('⚠️ pnpm 不可用，尝试使用 npm...');
    try {
      execSync('npm --version', { stdio: 'pipe' });
      console.log('✅ npm 可用\n');
      return 'npm';
    } catch (npmError) {
      console.error('❌ 没有可用的包管理器 (pnpm 或 npm)');
      process.exit(1);
    }
  }
}

// 安装依赖
function installDependencies(packageManager) {
  console.log('📥 检查依赖...');
  
  if (!fs.existsSync('node_modules')) {
    runCommand(`${packageManager} install`, '安装依赖');
  } else {
    console.log('✅ 依赖已安装\n');
  }
}

// 设置数据库
function setupDatabase(dbExists) {
  if (!dbExists) {
    runCommand('pnpm run db:push', '创建数据库表结构');
  } else {
    console.log('🔄 数据库已存在，跳过表结构创建\n');
  }

  // 生成Prisma客户端
  runCommand('pnpm run db:generate', '生成Prisma客户端');
  
  // 运行种子数据（如果需要）
  console.log('🌱 检查种子数据...');
  try {
    // 简单检查是否有数据
    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();
    
    prisma.user.count().then(count => {
      if (count === 0) {
        console.log('📝 数据库为空，运行种子数据...');
        runCommand('pnpm run db:seed', '初始化种子数据');
      } else {
        console.log('✅ 数据库已有数据，跳过种子初始化\n');
      }
      prisma.$disconnect();
    }).catch(() => {
      console.log('📝 无法检查数据，运行种子数据...');
      runCommand('pnpm run db:seed', '初始化种子数据');
    });
  } catch (error) {
    console.log('📝 运行种子数据...');
    runCommand('pnpm run db:seed', '初始化种子数据');
  }
}

// 验证设置
function validateSetup() {
  console.log('🧪 验证设置...');
  
  try {
    // 检查TypeScript编译
    execSync('pnpm run type-check', { stdio: 'pipe' });
    console.log('✅ TypeScript编译通过');

    // 检查构建
    execSync('pnpm run build', { stdio: 'pipe' });
    console.log('✅ 项目构建成功');

  } catch (error) {
    console.warn('⚠️ 验证过程中出现问题，但可以继续开发');
  }
  
  console.log('');
}

// 显示开发信息
function showDevInfo() {
  console.log('🎉 开发环境设置完成!\n');
  
  console.log('📚 可用命令:');
  console.log('   pnpm run dev         - 启动开发服务器');
  console.log('   pnpm run db:studio   - 打开数据库管理界面');
  console.log('   pnpm run db:seed     - 重新初始化种子数据');
  console.log('   pnpm run test        - 运行测试');
  console.log('   pnpm run lint        - 代码检查');
  console.log('');

  console.log('🛠️ 开发工具:');
  console.log('   - 开发工具面板: 按 Ctrl+Shift+D 打开');
  console.log('   - 数据库管理: pnpm run db:studio');
  console.log('   - API测试: 访问 http://localhost:4096/api/health');
  console.log('');

  console.log('🔗 有用链接:');
  console.log('   - 应用: http://localhost:4096');
  console.log('   - API文档: http://localhost:4096/api');
  console.log('   - 数据库: ./dev.db (SQLite)');
  console.log('');

  console.log('🚀 现在可以运行 "pnpm run dev" 启动开发服务器!');
}

// 主函数
function main() {
  try {
    checkNodeVersion();
    checkRequiredFiles();
    
    const packageManager = checkPackageManager();
    const dbExists = checkDatabase();
    
    installDependencies(packageManager);
    setupDatabase(dbExists);
    validateSetup();
    
    showDevInfo();
  } catch (error) {
    console.error('\n❌ 开发环境设置失败:', error.message);
    process.exit(1);
  }
}

// 运行主函数
main();
