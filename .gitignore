# Created by https://www.toptal.com/developers/gitignore/api/node,nextjs,react,macos,vercel
# Edit at https://www.toptal.com/developers/gitignore?templates=node,nextjs,react,macos,vercel
# Last updated: 2025-01-04

# ===================================================================
# macOS
# ===================================================================
# General
.DS_Store
.AppleDouble
.LSOverride

# Icon must end with two \r
Icon

# Thumbnails
._*

# Files that might appear in the root of a volume
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent

# Directories potentially created on remote AFP share
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# iCloud generated files
*.icloud

# ===================================================================
# Node.js
# ===================================================================
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Diagnostic reports (https://nodejs.org/api/report.html)
report.[0-9]*.[0-9]*.[0-9]*.[0-9]*.json

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules/
jspm_packages/

# Snowpack dependency directory (https://snowpack.dev/)
web_modules/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env
.env.development.local
.env.test.local
.env.production.local
.env.local
.env*.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Serverless Webpack directories
.webpack/

# SvelteKit build / generate output
.svelte-kit

# ===================================================================
# Next.js
# ===================================================================
# dependencies
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
*.pem

# typescript
*.tsbuildinfo
next-env.d.ts

# ===================================================================
# React
# ===================================================================
**/*.backup.*
**/*.back.*

*.sublime*

psd
thumb
sketch

# ===================================================================
# Vercel
# ===================================================================
.vercel

# ===================================================================
# Project Specific
# ===================================================================
# Prisma
/lib/generated/prisma
prisma/dev.db*
prisma/migrations/migration_lock.toml

# Test reports
test-report.json
/coverage/
jest-coverage/

# IDE
.vscode/
.idea/

# YoYo AI version control directory
.yoyo/

# Build artifacts
dist/
build/

# Temporary files
*.tmp
*.temp
.temp/

# OS generated files
Thumbs.db
ehthumbs.db