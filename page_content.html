<!DOCTYPE html><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="/_next/static/css/app/layout.css?v=1751507877030" data-precedence="next_static/css/app/layout.css"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack.js?v=1751507877030"/><script src="/_next/static/chunks/main-app.js?v=1751507877030" async=""></script><script src="/_next/static/chunks/app-pages-internals.js" async=""></script><script src="/_next/static/chunks/app/page.js" async=""></script><title>cube</title><meta name="description" content="@pokeby"/><meta property="og:title" content="cube"/><meta property="og:description" content="@pokeby"/><meta property="og:url" content="http://localhost:4096"/><meta property="og:site_name" content="cube"/><meta property="og:image" content="http://localhost:3003/images/cube.png"/><meta property="og:image:width" content="1200"/><meta property="og:image:height" content="630"/><meta property="og:image:alt" content="cube"/><meta property="og:type" content="website"/><meta name="twitter:card" content="summary_large_image"/><meta name="twitter:title" content="cube"/><meta name="twitter:description" content="@pokeby"/><meta name="twitter:image" content="http://localhost:3003/images/cube.png"/><meta name="twitter:image:width" content="1200"/><meta name="twitter:image:height" content="630"/><meta name="twitter:image:alt" content="cube"/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16"/><script src="/_next/static/chunks/polyfills.js" noModule=""></script></head><body class="rounded-none"><div class="flex h-screen bg-gray-900 text-white"><div class="flex-grow flex flex-col relative"><div class="flex-1 flex flex-col bg-white mr-2"><div class="flex-1 overflow-auto flex items-center justify-start" style="padding-top:20px;padding-left:20px;padding-bottom:20px;padding-right:0px;height:100%;min-height:100%"><div class="grid" style="grid-template-columns:repeat(33, 1fr);aspect-ratio:1/1;width:calc(100vh - 40px);height:calc(100vh - 40px);max-width:calc(100vh - 40px);max-height:calc(100vh - 40px);gap:0"></div></div><div class="absolute top-2 right-2 bg-black bg-opacity-75 text-white text-xs p-2 rounded">虚拟化: <!-- -->关闭<!-- --> | 可见单元格: <!-- -->0<!-- -->/<!-- -->0<!-- --> | 内存优化: <!-- -->NaN<!-- -->%</div></div></div><div class="w-72 bg-white flex flex-col overflow-hidden border-l border-gray-300 pl-2"><div class="space-y-1 px-1 py-2"><div class="border-b border-gray-200 pb-2"><div class="text-xs font-medium text-gray-500 mb-1 px-1">R2新架构</div><div class="grid grid-cols-3 gap-1"><button class="px-2 py-2 text-xs font-medium rounded-md transition-all duration-300 bg-gray-50 text-gray-600 border border-gray-200 hover:bg-gray-100"><div class="text-center"><div class="text-xs">样式</div></div></button><button class="px-2 py-2 text-xs font-medium rounded-md transition-all duration-300 bg-gray-50 text-gray-600 border border-gray-200 hover:bg-gray-100"><div class="text-center"><div class="text-xs">数据</div></div></button><button class="px-2 py-2 text-xs font-medium rounded-md transition-all duration-300 bg-gray-50 text-gray-600 border border-gray-200 hover:bg-gray-100"><div class="text-center"><div class="text-xs">组合</div></div></button></div></div></div><div class="flex-1 p-3 overflow-y-auto scrollbar-thin bg-white"><div class="p-4 bg-blue-50 rounded border border-blue-200"><div class="text-sm font-medium text-blue-800 mb-2">✨ 功能迁移提示</div><div class="text-xs text-blue-600 space-y-1"><div>• 传统面板功能已全部移植到R2新架构</div><div>• 请使用【样式】【数据】【组合】面板</div><div>• 体验更加统一高效的操作界面</div></div><button class="mt-3 w-full p-2 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">切换到R2样式面板</button></div></div></div></div><script src="/_next/static/chunks/webpack.js?v=1751507877030" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0]);self.__next_f.push([2,null])</script><script>self.__next_f.push([1,"1:HL[\"/_next/static/css/app/layout.css?v=1751507877030\",\"style\"]\n0:D{\"name\":\"r2\",\"env\":\"Server\"}\n"])</script><script>self.__next_f.push([1,"2:I[\"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js\",[\"app-pages-internals\",\"static/chunks/app-pages-internals.js\"],\"\"]\n4:I[\"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js\",[\"app-pages-internals\",\"static/chunks/app-pages-internals.js\"],\"ClientPageRoot\"]\n5:I[\"(app-pages-browser)/./app/page.tsx\",[\"app/page\",\"static/chunks/app/page.js\"],\"default\",1]\n7:I[\"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js\",[\"app-pages-internals\",\"static/chunks/app-pages-internals.js\"],\"\"]\n8:I[\"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js\",[\"app-pages-internals\",\"static/chunks/app-pages-internals.js\"],\"\"]\nc:I[\"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\",[\"app-pages-internals\",\"static/chunks/app-pages-internals.js\"],\"\"]\n3:D{\"name\":\"\",\"env\":\"Server\"}\n6:D{\"name\":\"RootLayout\",\"env\":\"Server\"}\n9:D{\"name\":\"NotFound\",\"env\":\"Server\"}\n9:[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":\"404\"}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page coul"])</script><script>self.__next_f.push([1,"d not be found.\"}]}]]}]}]]\n6:[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"rounded-none\",\"children\":[\"$\",\"$L7\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\"],\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L8\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$9\",\"notFoundStyles\":[]}]}]}]\na:D{\"name\":\"rQ\",\"env\":\"Server\"}\na:null\nb:D{\"name\":\"\",\"env\":\"Server\"}\nd:[]\n0:[\"$\",\"$L2\",null,{\"buildId\":\"development\",\"assetPrefix\":\"\",\"urlParts\":[\"\",\"\"],\"initialTree\":[\"\",{\"children\":[\"__PAGE__\",{}]},\"$undefined\",\"$undefined\",true],\"initialSeedData\":[\"\",{\"children\":[\"__PAGE__\",{},[[\"$L3\",[\"$\",\"$L4\",null,{\"props\":{\"params\":{},\"searchParams\":{}},\"Component\":\"$5\"}],null],null],null]},[[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/app/layout.css?v=1751507877030\",\"precedence\":\"next_static/css/app/layout.css\",\"crossOrigin\":\"$undefined\"}]],\"$6\"],null],null],\"couldBeIntercepted\":false,\"initialHead\":[\"$a\",\"$Lb\"],\"globalErrorComponent\":\"$c\",\"missingSlots\":\"$Wd\"}]\n"])</script><script>self.__next_f.push([1,"b:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}],[\"$\",\"meta\",\"1\",{\"charSet\":\"utf-8\"}],[\"$\",\"title\",\"2\",{\"children\":\"cube\"}],[\"$\",\"meta\",\"3\",{\"name\":\"description\",\"content\":\"@pokeby\"}],[\"$\",\"meta\",\"4\",{\"property\":\"og:title\",\"content\":\"cube\"}],[\"$\",\"meta\",\"5\",{\"property\":\"og:description\",\"content\":\"@pokeby\"}],[\"$\",\"meta\",\"6\",{\"property\":\"og:url\",\"content\":\"http://localhost:4096\"}],[\"$\",\"meta\",\"7\",{\"property\":\"og:site_name\",\"content\":\"cube\"}],[\"$\",\"meta\",\"8\",{\"property\":\"og:image\",\"content\":\"http://localhost:3003/images/cube.png\"}],[\"$\",\"meta\",\"9\",{\"property\":\"og:image:width\",\"content\":\"1200\"}],[\"$\",\"meta\",\"10\",{\"property\":\"og:image:height\",\"content\":\"630\"}],[\"$\",\"meta\",\"11\",{\"property\":\"og:image:alt\",\"content\":\"cube\"}],[\"$\",\"meta\",\"12\",{\"property\":\"og:type\",\"content\":\"website\"}],[\"$\",\"meta\",\"13\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"}],[\"$\",\"meta\",\"14\",{\"name\":\"twitter:title\",\"content\":\"cube\"}],[\"$\",\"meta\",\"15\",{\"name\":\"twitter:description\",\"content\":\"@pokeby\"}],[\"$\",\"meta\",\"16\",{\"name\":\"twitter:image\",\"content\":\"http://localhost:3003/images/cube.png\"}],[\"$\",\"meta\",\"17\",{\"name\":\"twitter:image:width\",\"content\":\"1200\"}],[\"$\",\"meta\",\"18\",{\"name\":\"twitter:image:height\",\"content\":\"630\"}],[\"$\",\"meta\",\"19\",{\"name\":\"twitter:image:alt\",\"content\":\"cube\"}],[\"$\",\"link\",\"20\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}]]\n3:null\n"])</script></body></html>