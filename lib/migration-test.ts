/**
 * 数据迁移测试工具
 * 🎯 核心价值：验证迁移数据的完整性和正确性
 * 🔄 测试策略：对比LocalStorage和数据库数据
 * ⚡ 安全保障：确保迁移过程无数据丢失
 */

import prisma from './prisma';
import { 
  BasicDataState, 
  BusinessDataState, 
  CombinationDataState, 
  StyleState, 
  DynamicStyleState 
} from '@/stores';

export interface MigrationTestResult {
  success: boolean;
  errors: string[];
  warnings: string[];
  summary: {
    totalChecks: number;
    passedChecks: number;
    failedChecks: number;
  };
}

// 从LocalStorage读取数据（测试用）
function getLocalStorageData<T>(storeName: string): T | null {
  if (typeof window === 'undefined') return null;
  
  try {
    const data = localStorage.getItem(storeName);
    if (!data) return null;
    
    const parsed = JSON.parse(data);
    return parsed.state || parsed;
  } catch (error) {
    console.error(`读取 ${storeName} 数据失败:`, error);
    return null;
  }
}

// 验证基础数据迁移
async function validateBasicDataMigration(
  projectId: string, 
  originalData: BasicDataState
): Promise<{ errors: string[]; warnings: string[] }> {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  try {
    // 验证颜色数据
    const colorTypes = ['red', 'cyan', 'yellow', 'purple', 'orange', 'green', 'blue', 'pink'] as const;
    
    for (const colorType of colorTypes) {
      const originalColorCoords = originalData.colorCoordinates[colorType];
      const originalColorValue = originalData.colorValues[colorType];
      
      for (let level = 1; level <= 4; level++) {
        const levelKey = `level${level}` as keyof typeof originalColorCoords;
        const originalLevelCoords = originalColorCoords[levelKey];
        
        if (originalLevelCoords && originalLevelCoords.length > 0) {
          const dbColorData = await prisma.colorData.findUnique({
            where: {
              projectId_colorType_level: {
                projectId,
                colorType,
                level,
              },
            },
          });
          
          if (!dbColorData) {
            errors.push(`缺少颜色数据: ${colorType} Level ${level}`);
            continue;
          }
          
          const dbCoords = JSON.parse(dbColorData.coordinates);
          if (JSON.stringify(originalLevelCoords) !== JSON.stringify(dbCoords)) {
            errors.push(`坐标数据不匹配: ${colorType} Level ${level}`);
          }
          
          if (originalColorValue.value !== dbColorData.colorValue) {
            warnings.push(`颜色值不匹配: ${colorType}`);
          }
        }
      }
    }
    
    // 验证网格数据
    if (originalData.gridData && originalData.gridData.length > 0) {
      const dbGridCount = await prisma.gridData.count({
        where: { projectId },
      });
      
      if (dbGridCount !== originalData.gridData.length) {
        errors.push(`网格数据数量不匹配: 原始${originalData.gridData.length}, 数据库${dbGridCount}`);
      }
    }
    
    // 验证黑色格子数据
    if (originalData.blackCellData?.coordinates) {
      const dbBlackCellCount = await prisma.blackCellData.count({
        where: { projectId },
      });
      
      if (dbBlackCellCount !== originalData.blackCellData.coordinates.length) {
        errors.push(`黑色格子数据数量不匹配: 原始${originalData.blackCellData.coordinates.length}, 数据库${dbBlackCellCount}`);
      }
    }
    
  } catch (error) {
    errors.push(`验证基础数据时出错: ${error instanceof Error ? error.message : '未知错误'}`);
  }
  
  return { errors, warnings };
}

// 验证项目设置迁移
async function validateProjectSettingsMigration(
  projectId: string,
  originalStyleData: StyleState,
  originalDynamicData: DynamicStyleState
): Promise<{ errors: string[]; warnings: string[] }> {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  try {
    const dbSettings = await prisma.projectSettings.findUnique({
      where: { projectId },
    });
    
    if (!dbSettings) {
      errors.push('项目设置未找到');
      return { errors, warnings };
    }
    
    // 验证样式设置
    if (dbSettings.currentTheme !== originalStyleData.currentTheme) {
      errors.push('主题设置不匹配');
    }
    
    if (dbSettings.showBlackCells !== originalStyleData.showBlackCells) {
      errors.push('黑色格子显示设置不匹配');
    }
    
    // 验证动态样式设置
    if (dbSettings.fontSize !== originalDynamicData.fontSize) {
      errors.push('字体大小设置不匹配');
    }
    
    if (dbSettings.cellShape !== originalDynamicData.cellShape) {
      errors.push('格子形状设置不匹配');
    }
    
    if (dbSettings.displayMode !== originalDynamicData.displayMode) {
      errors.push('显示模式设置不匹配');
    }
    
  } catch (error) {
    errors.push(`验证项目设置时出错: ${error instanceof Error ? error.message : '未知错误'}`);
  }
  
  return { errors, warnings };
}

// 验证版本数据迁移
async function validateVersionMigration(
  projectId: string,
  originalBusinessData: BusinessDataState
): Promise<{ errors: string[]; warnings: string[] }> {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  try {
    const dbVersions = await prisma.version.findMany({
      where: { projectId },
    });
    
    // 计算原始版本总数
    const originalVersionCount = 
      Object.keys(originalBusinessData.defaultVersions || {}).length +
      Object.keys(originalBusinessData.groupModeVersions || {}).length +
      Object.keys(originalBusinessData.mixedModeVersions || {}).length +
      Object.keys(originalBusinessData.matrixModeVersions || {}).length;
    
    if (dbVersions.length !== originalVersionCount) {
      errors.push(`版本数量不匹配: 原始${originalVersionCount}, 数据库${dbVersions.length}`);
    }
    
    // 验证当前版本设置
    const currentDefaultVersion = dbVersions.find(v => 
      v.versionType === 'default' && v.isCurrent
    );
    
    if (currentDefaultVersion?.name !== originalBusinessData.currentDefaultVersion) {
      warnings.push('当前默认版本不匹配');
    }
    
  } catch (error) {
    errors.push(`验证版本数据时出错: ${error instanceof Error ? error.message : '未知错误'}`);
  }
  
  return { errors, warnings };
}

// 主测试函数
export async function testMigration(projectId: string): Promise<MigrationTestResult> {
  const result: MigrationTestResult = {
    success: true,
    errors: [],
    warnings: [],
    summary: {
      totalChecks: 0,
      passedChecks: 0,
      failedChecks: 0,
    },
  };
  
  try {
    console.log('🧪 开始迁移数据验证...');
    
    // 读取原始LocalStorage数据
    const basicData = getLocalStorageData<BasicDataState>('basic-data-store');
    const styleData = getLocalStorageData<StyleState>('style-store');
    const dynamicData = getLocalStorageData<DynamicStyleState>('dynamic-style-store');
    const businessData = getLocalStorageData<BusinessDataState>('business-data-store');
    const combinationData = getLocalStorageData<CombinationDataState>('combination-data-storage');
    
    // 验证基础数据
    if (basicData) {
      const basicValidation = await validateBasicDataMigration(projectId, basicData);
      result.errors.push(...basicValidation.errors);
      result.warnings.push(...basicValidation.warnings);
      result.summary.totalChecks++;
      if (basicValidation.errors.length === 0) {
        result.summary.passedChecks++;
      } else {
        result.summary.failedChecks++;
      }
    }
    
    // 验证项目设置
    if (styleData && dynamicData) {
      const settingsValidation = await validateProjectSettingsMigration(projectId, styleData, dynamicData);
      result.errors.push(...settingsValidation.errors);
      result.warnings.push(...settingsValidation.warnings);
      result.summary.totalChecks++;
      if (settingsValidation.errors.length === 0) {
        result.summary.passedChecks++;
      } else {
        result.summary.failedChecks++;
      }
    }
    
    // 验证版本数据
    if (businessData) {
      const versionValidation = await validateVersionMigration(projectId, businessData);
      result.errors.push(...versionValidation.errors);
      result.warnings.push(...versionValidation.warnings);
      result.summary.totalChecks++;
      if (versionValidation.errors.length === 0) {
        result.summary.passedChecks++;
      } else {
        result.summary.failedChecks++;
      }
    }
    
    result.success = result.errors.length === 0;
    
    if (result.success) {
      console.log('✅ 迁移数据验证通过');
    } else {
      console.log('❌ 迁移数据验证失败');
    }
    
  } catch (error) {
    result.success = false;
    result.errors.push(`验证过程出错: ${error instanceof Error ? error.message : '未知错误'}`);
    console.error('❌ 迁移验证过程出错:', error);
  }
  
  return result;
}
