/**
 * Prisma客户端实例 - 全栈架构核心
 * 🎯 核心价值：统一数据库访问，支持连接池和优化
 * 🔄 环境支持：SQLite开发环境 + PostgreSQL生产环境
 * ⚡ 性能优化：单例模式，避免重复连接
 */

import { PrismaClient } from '@prisma/client';

// 全局类型声明，避免TypeScript错误
declare global {
  var prisma: PrismaClient | undefined;
}

// 创建Prisma客户端实例
const prisma = globalThis.prisma || new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
});

// 在开发环境中将实例保存到全局变量，避免热重载时重复创建连接
if (process.env.NODE_ENV === 'development') {
  globalThis.prisma = prisma;
}

export default prisma;

// 数据库连接测试函数
export async function testDatabaseConnection() {
  try {
    await prisma.$connect();
    console.log('✅ 数据库连接成功');
    return true;
  } catch (error) {
    console.error('❌ 数据库连接失败:', error);
    return false;
  }
}

// 优雅关闭数据库连接
export async function closeDatabaseConnection() {
  try {
    await prisma.$disconnect();
    console.log('✅ 数据库连接已关闭');
  } catch (error) {
    console.error('❌ 关闭数据库连接时出错:', error);
  }
}

// 数据库健康检查
export async function healthCheck() {
  try {
    const result = await prisma.$queryRaw`SELECT 1 as health`;
    return { status: 'healthy', result };
  } catch (error) {
    return { status: 'unhealthy', error: error instanceof Error ? error.message : 'Unknown error' };
  }
}
