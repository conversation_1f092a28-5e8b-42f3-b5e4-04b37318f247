/**
 * API工具函数 - 全栈架构
 * 🎯 核心价值：统一API响应处理，错误管理，验证逻辑
 * 🔄 标准化：一致的响应格式和错误处理
 * ⚡ 类型安全：完整的TypeScript支持
 */

import { NextRequest, NextResponse } from 'next/server';
import { ApiResponse } from '@/types/api';

// === API响应工具函数 ===

// 成功响应
export function successResponse<T>(data: T, message?: string): NextResponse<ApiResponse<T>> {
  return NextResponse.json({
    success: true,
    data,
    message,
    timestamp: new Date().toISOString(),
  });
}

// 错误响应
export function errorResponse(error: string, status: number = 400): NextResponse<ApiResponse> {
  return NextResponse.json({
    success: false,
    error,
    timestamp: new Date().toISOString(),
  }, { status });
}

// 验证错误响应
export function validationErrorResponse(errors: string[]): NextResponse<ApiResponse> {
  return NextResponse.json({
    success: false,
    error: '数据验证失败',
    message: errors.join(', '),
    timestamp: new Date().toISOString(),
  }, { status: 422 });
}

// 未找到响应
export function notFoundResponse(resource: string = '资源'): NextResponse<ApiResponse> {
  return NextResponse.json({
    success: false,
    error: `${resource}未找到`,
    timestamp: new Date().toISOString(),
  }, { status: 404 });
}

// 服务器错误响应
export function serverErrorResponse(error?: string): NextResponse<ApiResponse> {
  return NextResponse.json({
    success: false,
    error: error || '服务器内部错误',
    timestamp: new Date().toISOString(),
  }, { status: 500 });
}

// === 请求处理工具 ===

// 安全解析JSON请求体
export async function parseRequestBody<T>(request: NextRequest): Promise<T | null> {
  try {
    const body = await request.json();
    return body as T;
  } catch (error) {
    console.error('解析请求体失败:', error);
    return null;
  }
}

// 获取查询参数
export function getQueryParams(request: NextRequest): URLSearchParams {
  const url = new URL(request.url);
  return url.searchParams;
}

// 获取路径参数
export function getPathParams(request: NextRequest, pattern: string): Record<string, string> {
  const url = new URL(request.url);
  const pathParts = url.pathname.split('/');
  const patternParts = pattern.split('/');
  
  const params: Record<string, string> = {};
  
  for (let i = 0; i < patternParts.length; i++) {
    const part = patternParts[i];
    if (part.startsWith('[') && part.endsWith(']')) {
      const paramName = part.slice(1, -1);
      params[paramName] = pathParts[i] || '';
    }
  }
  
  return params;
}

// === 验证工具函数 ===

// 验证必需字段
export function validateRequired(data: any, fields: string[]): string[] {
  const errors: string[] = [];
  
  for (const field of fields) {
    if (!data[field] && data[field] !== 0 && data[field] !== false) {
      errors.push(`${field} 是必需的`);
    }
  }
  
  return errors;
}

// 验证字符串长度
export function validateStringLength(
  value: string, 
  fieldName: string, 
  min?: number, 
  max?: number
): string[] {
  const errors: string[] = [];
  
  if (min && value.length < min) {
    errors.push(`${fieldName} 长度不能少于 ${min} 个字符`);
  }
  
  if (max && value.length > max) {
    errors.push(`${fieldName} 长度不能超过 ${max} 个字符`);
  }
  
  return errors;
}

// 验证数字范围
export function validateNumberRange(
  value: number, 
  fieldName: string, 
  min?: number, 
  max?: number
): string[] {
  const errors: string[] = [];
  
  if (min !== undefined && value < min) {
    errors.push(`${fieldName} 不能小于 ${min}`);
  }
  
  if (max !== undefined && value > max) {
    errors.push(`${fieldName} 不能大于 ${max}`);
  }
  
  return errors;
}

// 验证枚举值
export function validateEnum(
  value: string, 
  fieldName: string, 
  allowedValues: string[]
): string[] {
  const errors: string[] = [];
  
  if (!allowedValues.includes(value)) {
    errors.push(`${fieldName} 必须是以下值之一: ${allowedValues.join(', ')}`);
  }
  
  return errors;
}

// 验证坐标格式
export function validateCoordinates(coordinates: any, fieldName: string = 'coordinates'): string[] {
  const errors: string[] = [];
  
  if (!Array.isArray(coordinates)) {
    errors.push(`${fieldName} 必须是数组`);
    return errors;
  }
  
  for (let i = 0; i < coordinates.length; i++) {
    const coord = coordinates[i];
    if (!Array.isArray(coord) || coord.length !== 2) {
      errors.push(`${fieldName}[${i}] 必须是包含两个元素的数组`);
      continue;
    }
    
    const [x, y] = coord;
    if (typeof x !== 'number' || typeof y !== 'number') {
      errors.push(`${fieldName}[${i}] 的坐标必须是数字`);
    }
  }
  
  return errors;
}

// === 错误处理工具 ===

// 包装异步API处理函数
export function withErrorHandling<T extends any[], R>(
  handler: (...args: T) => Promise<NextResponse<R>>
) {
  return async (...args: T): Promise<NextResponse<R | ApiResponse>> => {
    try {
      return await handler(...args);
    } catch (error) {
      console.error('API处理错误:', error);
      
      if (error instanceof Error) {
        return serverErrorResponse(error.message);
      }
      
      return serverErrorResponse();
    }
  };
}

// 数据库错误处理
export function handleDatabaseError(error: any): NextResponse<ApiResponse> {
  console.error('数据库错误:', error);
  
  // Prisma特定错误处理
  if (error.code === 'P2002') {
    return errorResponse('数据已存在，违反唯一性约束', 409);
  }
  
  if (error.code === 'P2025') {
    return notFoundResponse('记录');
  }
  
  if (error.code === 'P2003') {
    return errorResponse('外键约束违反', 400);
  }
  
  return serverErrorResponse('数据库操作失败');
}

// === 分页工具 ===

// 计算分页参数
export function calculatePagination(page: number, limit: number, total: number) {
  const totalPages = Math.ceil(total / limit);
  const offset = (page - 1) * limit;
  
  return {
    page,
    limit,
    total,
    totalPages,
    offset,
    hasNext: page < totalPages,
    hasPrev: page > 1,
  };
}

// 解析分页查询参数
export function parsePaginationParams(searchParams: URLSearchParams) {
  const page = Math.max(1, parseInt(searchParams.get('page') || '1', 10));
  const limit = Math.min(100, Math.max(1, parseInt(searchParams.get('limit') || '20', 10)));
  
  return { page, limit };
}

// === 缓存工具 ===

// 设置缓存头
export function setCacheHeaders(response: NextResponse, maxAge: number = 300) {
  response.headers.set('Cache-Control', `public, max-age=${maxAge}, s-maxage=${maxAge}`);
  return response;
}

// 设置无缓存头
export function setNoCacheHeaders(response: NextResponse) {
  response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
  response.headers.set('Pragma', 'no-cache');
  response.headers.set('Expires', '0');
  return response;
}
