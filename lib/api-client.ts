/**
 * API客户端 - 前端API调用封装
 * 🎯 核心价值：统一API调用接口，错误处理，类型安全
 * 🔄 自动重试：网络错误自动重试机制
 * ⚡ 缓存支持：智能缓存和失效策略
 */

import { 
  ApiResponse, 
  PaginatedResponse,
  UserResponse,
  ProjectResponse,
  ColorDataRequest,
  ColorDataResponse,
  GridDataRequest,
  GridDataResponse,
  ProjectSettingsRequest,
  ProjectSettingsResponse,
  MigrationRequest,
  MigrationResponse,
  CreateUserRequest,
  CreateProjectRequest,
} from '@/types/api';

// API基础配置
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '/api';
const DEFAULT_TIMEOUT = 10000; // 10秒超时

// 请求配置接口
interface RequestConfig {
  timeout?: number;
  retries?: number;
  cache?: boolean;
}

// API错误类
export class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public response?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// 基础请求函数
async function request<T>(
  endpoint: string,
  options: RequestInit & RequestConfig = {}
): Promise<T> {
  const { timeout = DEFAULT_TIMEOUT, retries = 3, cache = false, ...fetchOptions } = options;
  
  const url = `${API_BASE_URL}${endpoint}`;
  
  // 设置默认headers
  const headers = {
    'Content-Type': 'application/json',
    ...fetchOptions.headers,
  };
  
  const config: RequestInit = {
    ...fetchOptions,
    headers,
  };
  
  // 添加超时控制
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);
  config.signal = controller.signal;
  
  let lastError: Error;
  
  // 重试机制
  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      const response = await fetch(url, config);
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new ApiError(
          errorData.error || `HTTP ${response.status}`,
          response.status,
          errorData
        );
      }
      
      const data: ApiResponse<T> = await response.json();
      
      if (!data.success) {
        throw new ApiError(data.error || '请求失败', 400, data);
      }
      
      return data.data as T;
    } catch (error) {
      lastError = error as Error;
      
      // 如果是最后一次尝试或者不是网络错误，直接抛出
      if (attempt === retries || !(error instanceof TypeError)) {
        break;
      }
      
      // 等待后重试
      await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
    }
  }
  
  clearTimeout(timeoutId);
  throw lastError!;
}

// === 用户API ===
export const userApi = {
  // 获取所有用户
  getAll: (): Promise<UserResponse[]> => 
    request<UserResponse[]>('/users'),
  
  // 获取特定用户
  getById: (id: string): Promise<UserResponse> => 
    request<UserResponse>(`/users/${id}`),
  
  // 创建用户
  create: (data: CreateUserRequest): Promise<UserResponse> => 
    request<UserResponse>('/users', {
      method: 'POST',
      body: JSON.stringify(data),
    }),
  
  // 更新用户
  update: (id: string, data: Partial<CreateUserRequest>): Promise<UserResponse> => 
    request<UserResponse>(`/users/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    }),
  
  // 删除用户
  delete: (id: string): Promise<void> => 
    request<void>(`/users/${id}`, {
      method: 'DELETE',
    }),
};

// === 项目API ===
export const projectApi = {
  // 获取项目列表
  getAll: (userId?: string): Promise<{ projects: ProjectResponse[]; pagination: any }> => {
    const params = userId ? `?userId=${userId}` : '';
    return request<{ projects: ProjectResponse[]; pagination: any }>(`/projects${params}`);
  },
  
  // 获取特定项目
  getById: (id: string): Promise<ProjectResponse> => 
    request<ProjectResponse>(`/projects/${id}`),
  
  // 创建项目
  create: (data: CreateProjectRequest & { userId: string }): Promise<ProjectResponse> => 
    request<ProjectResponse>('/projects', {
      method: 'POST',
      body: JSON.stringify(data),
    }),
  
  // 更新项目
  update: (id: string, data: Partial<CreateProjectRequest>): Promise<ProjectResponse> => 
    request<ProjectResponse>(`/projects/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    }),
  
  // 删除项目
  delete: (id: string): Promise<void> => 
    request<void>(`/projects/${id}`, {
      method: 'DELETE',
    }),
};

// === 颜色数据API ===
export const colorDataApi = {
  // 获取项目的颜色数据
  getByProject: (
    projectId: string, 
    query?: { colorType?: string; level?: number; visible?: boolean }
  ): Promise<ColorDataResponse[]> => {
    const params = new URLSearchParams();
    if (query?.colorType) params.append('colorType', query.colorType);
    if (query?.level) params.append('level', query.level.toString());
    if (query?.visible !== undefined) params.append('visible', query.visible.toString());
    
    const queryString = params.toString();
    const url = `/projects/${projectId}/colors${queryString ? `?${queryString}` : ''}`;
    
    return request<ColorDataResponse[]>(url);
  },
  
  // 创建或更新颜色数据
  upsert: (projectId: string, data: ColorDataRequest): Promise<ColorDataResponse> => 
    request<ColorDataResponse>(`/projects/${projectId}/colors`, {
      method: 'POST',
      body: JSON.stringify(data),
    }),
  
  // 批量更新颜色数据
  batchUpdate: (projectId: string, operations: any[]): Promise<ColorDataResponse[]> => 
    request<ColorDataResponse[]>(`/projects/${projectId}/colors/batch`, {
      method: 'POST',
      body: JSON.stringify({ operations }),
    }),
};

// === 项目设置API ===
export const projectSettingsApi = {
  // 获取项目设置
  get: (projectId: string): Promise<ProjectSettingsResponse> => 
    request<ProjectSettingsResponse>(`/projects/${projectId}/settings`),
  
  // 更新项目设置
  update: (projectId: string, data: ProjectSettingsRequest): Promise<ProjectSettingsResponse> => 
    request<ProjectSettingsResponse>(`/projects/${projectId}/settings`, {
      method: 'PUT',
      body: JSON.stringify(data),
    }),
};

// === 数据迁移API ===
export const migrationApi = {
  // 检查是否需要迁移
  checkNeed: (): Promise<{ needsMigration: boolean; message: string }> => 
    request<{ needsMigration: boolean; message: string }>('/migration'),
  
  // 执行迁移
  migrate: (data: MigrationRequest): Promise<MigrationResponse> => 
    request<MigrationResponse>('/migration', {
      method: 'POST',
      body: JSON.stringify(data),
      timeout: 30000, // 迁移可能需要更长时间
    }),
};

// === 工具函数 ===

// 检查网络连接
export async function checkConnection(): Promise<boolean> {
  try {
    await request('/health', { timeout: 5000 });
    return true;
  } catch {
    return false;
  }
}

// 批量请求
export async function batchRequest<T>(
  requests: Array<() => Promise<T>>
): Promise<Array<T | Error>> {
  const results = await Promise.allSettled(
    requests.map(request => request())
  );
  
  return results.map(result => 
    result.status === 'fulfilled' ? result.value : result.reason
  );
}

// 缓存管理
class ApiCache {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();
  
  set(key: string, data: any, ttl: number = 300000): void { // 默认5分钟
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    });
  }
  
  get(key: string): any | null {
    const item = this.cache.get(key);
    if (!item) return null;
    
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return item.data;
  }
  
  clear(): void {
    this.cache.clear();
  }
  
  delete(key: string): void {
    this.cache.delete(key);
  }
}

export const apiCache = new ApiCache();

// === 健康检查API ===
export const healthApi = {
  // 检查API健康状态
  check: (): Promise<{ status: string; timestamp: string }> =>
    request<{ status: string; timestamp: string }>('/health'),
};
