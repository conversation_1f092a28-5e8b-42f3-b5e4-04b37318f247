/**
 * 数据迁移工具 - LocalStorage到数据库
 * 🎯 核心价值：无缝迁移现有用户数据，确保零数据丢失
 * 🔄 迁移策略：渐进式迁移，保持向后兼容
 * ⚡ 安全保障：备份机制，回滚支持
 */

import prisma from './prisma';
import { 
  BasicDataState, 
  BusinessDataState, 
  CombinationDataState, 
  StyleState, 
  DynamicStyleState 
} from '@/stores';

// 迁移状态类型
export interface MigrationStatus {
  isCompleted: boolean;
  lastMigrationDate?: Date;
  migratedStores: string[];
  errors: string[];
}

// 从LocalStorage读取Zustand store数据
function getLocalStorageData<T>(storeName: string): T | null {
  if (typeof window === 'undefined') return null;
  
  try {
    const data = localStorage.getItem(storeName);
    if (!data) return null;
    
    const parsed = JSON.parse(data);
    return parsed.state || parsed;
  } catch (error) {
    console.error(`读取 ${storeName} 数据失败:`, error);
    return null;
  }
}

// 创建默认项目
async function createDefaultProject(userId: string): Promise<string> {
  const project = await prisma.project.create({
    data: {
      name: 'Cube1_Group 主项目',
      description: '从LocalStorage迁移的数据',
      userId,
    },
  });
  
  return project.id;
}

// 迁移基础数据 (basicDataStore)
async function migrateBasicData(projectId: string, data: BasicDataState) {
  console.log('🔄 迁移基础数据...');
  
  // 迁移颜色坐标数据
  const colorTypes = ['red', 'cyan', 'yellow', 'purple', 'orange', 'green', 'blue', 'pink'] as const;
  
  for (const colorType of colorTypes) {
    const colorCoords = data.colorCoordinates[colorType];
    const colorValue = data.colorValues[colorType];
    const visibility = data.colorVisibility[colorType];
    
    // 为每个层级创建颜色数据记录
    for (let level = 1; level <= 4; level++) {
      const levelCoords = colorCoords[`level${level}` as keyof typeof colorCoords];
      if (levelCoords && levelCoords.length > 0) {
        await prisma.colorData.upsert({
          where: {
            projectId_colorType_level: {
              projectId,
              colorType,
              level,
            },
          },
          update: {
            coordinates: JSON.stringify(levelCoords),
            colorValue: colorValue.value,
            visible: visibility[`level${level}` as keyof typeof visibility] ?? true,
          },
          create: {
            projectId,
            colorType,
            level,
            coordinates: JSON.stringify(levelCoords),
            colorValue: colorValue.value,
            visible: visibility[`level${level}` as keyof typeof visibility] ?? true,
          },
        });
      }
    }
  }
  
  // 迁移网格数据
  if (data.gridData && data.gridData.length > 0) {
    for (const cell of data.gridData) {
      await prisma.gridData.upsert({
        where: {
          projectId_row_col: {
            projectId,
            row: cell.row,
            col: cell.col,
          },
        },
        update: {
          x: cell.x,
          y: cell.y,
          number: cell.number,
          color: cell.color,
          level: cell.level,
          groupId: cell.group,
        },
        create: {
          projectId,
          row: cell.row,
          col: cell.col,
          x: cell.x,
          y: cell.y,
          number: cell.number,
          color: cell.color,
          level: cell.level,
          groupId: cell.group,
        },
      });
    }
  }
  
  // 迁移黑色格子数据
  if (data.blackCellData?.coordinates) {
    for (const blackCell of data.blackCellData.coordinates) {
      const [x, y] = blackCell.coords;
      await prisma.blackCellData.upsert({
        where: {
          projectId_x_y: {
            projectId,
            x,
            y,
          },
        },
        update: {
          letter: blackCell.letter,
          visible: data.blackCellData.visibility,
        },
        create: {
          projectId,
          x,
          y,
          letter: blackCell.letter,
          visible: data.blackCellData.visibility,
        },
      });
    }
  }
  
  console.log('✅ 基础数据迁移完成');
}

// 迁移项目设置 (styleStore + dynamicStyleStore)
async function migrateProjectSettings(
  projectId: string,
  styleData: StyleState,
  dynamicData: DynamicStyleState
) {
  console.log('🔄 迁移项目设置...');

  await prisma.projectSettings.upsert({
    where: { projectId },
    update: {
      // 样式设置
      currentTheme: styleData.currentTheme,
      showBlackCells: styleData.showBlackCells,
      matrixStyles: JSON.stringify(styleData.matrixStyles),
      colorScheme: JSON.stringify(styleData.colorScheme),

      // 动态样式设置
      fontSize: dynamicData.fontSize,
      matrixMargin: dynamicData.matrixMargin,
      cellShape: dynamicData.cellShape,
      displayMode: dynamicData.displayMode,
      enableCircleScale: dynamicData.enableCircleScale,
      circleScaleFactor: dynamicData.circleScaleFactor,
      enableVirtualization: dynamicData.enableVirtualization,

      // 全局显示控制
      showAllNumbers: dynamicData.showAllNumbers,
      showAllColors: dynamicData.showAllColors,
      showAllLevel1: dynamicData.showAllLevel1,
      showAllLevel2: dynamicData.showAllLevel2,
      showAllLevel3: dynamicData.showAllLevel3,
      showAllLevel4: dynamicData.showAllLevel4,
    },
    create: {
      projectId,
      // 样式设置
      currentTheme: styleData.currentTheme,
      showBlackCells: styleData.showBlackCells,
      matrixStyles: JSON.stringify(styleData.matrixStyles),
      colorScheme: JSON.stringify(styleData.colorScheme),

      // 动态样式设置
      fontSize: dynamicData.fontSize,
      matrixMargin: dynamicData.matrixMargin,
      cellShape: dynamicData.cellShape,
      displayMode: dynamicData.displayMode,
      enableCircleScale: dynamicData.enableCircleScale,
      circleScaleFactor: dynamicData.circleScaleFactor,
      enableVirtualization: dynamicData.enableVirtualization,

      // 全局显示控制
      showAllNumbers: dynamicData.showAllNumbers,
      showAllColors: dynamicData.showAllColors,
      showAllLevel1: dynamicData.showAllLevel1,
      showAllLevel2: dynamicData.showAllLevel2,
      showAllLevel3: dynamicData.showAllLevel3,
      showAllLevel4: dynamicData.showAllLevel4,
    },
  });

  console.log('✅ 项目设置迁移完成');
}

// 迁移业务数据 (businessDataStore)
async function migrateBusinessData(projectId: string, data: BusinessDataState) {
  console.log('🔄 迁移业务数据...');

  // 迁移版本数据
  const versionTypes = [
    { versions: data.defaultVersions, current: data.currentDefaultVersion, type: 'default' },
    { versions: data.groupModeVersions, current: data.currentGroupModeVersion, type: 'group' },
    { versions: data.mixedModeVersions, current: data.currentMixedModeVersion, type: 'mixed' },
    { versions: data.matrixModeVersions, current: data.currentMatrixModeVersion, type: 'matrix' },
  ];

  for (const versionGroup of versionTypes) {
    if (versionGroup.versions) {
      for (const [versionName, versionData] of Object.entries(versionGroup.versions)) {
        await prisma.version.upsert({
          where: {
            projectId_name_versionType: {
              projectId,
              name: versionName,
              versionType: versionGroup.type,
            },
          },
          update: {
            description: versionData.description,
            data: JSON.stringify(versionData.data),
            isCurrent: versionName === versionGroup.current,
          },
          create: {
            projectId,
            name: versionName,
            description: versionData.description,
            versionType: versionGroup.type,
            data: JSON.stringify(versionData.data),
            isCurrent: versionName === versionGroup.current,
          },
        });
      }
    }
  }

  console.log('✅ 业务数据迁移完成');
}

// 迁移组合数据 (combinationDataStore)
async function migrateCombinationData(projectId: string, data: CombinationDataState) {
  console.log('🔄 迁移组合数据...');

  await prisma.combinationData.upsert({
    where: { projectId },
    update: {
      currentMode: data.currentMode,
      showAllColors: data.defaultModeConfig.showAllColors,
      showAllLevels: data.defaultModeConfig.showAllLevels,
      selectedGroups: JSON.stringify(
        Object.fromEntries(
          Object.entries(data.selectedGroups).map(([color, groups]) => [
            color,
            Array.from(groups as Set<number>)
          ])
        )
      ),
      swapGroupsData: JSON.stringify(data.swapGroupsData),
      modeActivation: JSON.stringify(data.modeActivation),
      operationHistory: JSON.stringify(data.operationHistory),
      shortcutEnabled: data.shortcutEnabled,
    },
    create: {
      projectId,
      currentMode: data.currentMode,
      showAllColors: data.defaultModeConfig.showAllColors,
      showAllLevels: data.defaultModeConfig.showAllLevels,
      selectedGroups: JSON.stringify(
        Object.fromEntries(
          Object.entries(data.selectedGroups).map(([color, groups]) => [
            color,
            Array.from(groups as Set<number>)
          ])
        )
      ),
      swapGroupsData: JSON.stringify(data.swapGroupsData),
      modeActivation: JSON.stringify(data.modeActivation),
      operationHistory: JSON.stringify(data.operationHistory),
      shortcutEnabled: data.shortcutEnabled,
    },
  });

  console.log('✅ 组合数据迁移完成');
}

// 主迁移函数
export async function migrateFromLocalStorage(userId: string): Promise<MigrationStatus> {
  const status: MigrationStatus = {
    isCompleted: false,
    migratedStores: [],
    errors: [],
  };
  
  try {
    console.log('🚀 开始数据迁移...');
    
    // 创建默认项目
    const projectId = await createDefaultProject(userId);
    
    // 读取LocalStorage数据
    const basicData = getLocalStorageData<BasicDataState>('basic-data-store');
    const styleData = getLocalStorageData<StyleState>('style-store');
    const dynamicData = getLocalStorageData<DynamicStyleState>('dynamic-style-store');
    const businessData = getLocalStorageData<BusinessDataState>('business-data-store');
    const combinationData = getLocalStorageData<CombinationDataState>('combination-data-storage');
    
    // 迁移基础数据
    if (basicData) {
      await migrateBasicData(projectId, basicData);
      status.migratedStores.push('basic-data-store');
    }
    
    // 迁移项目设置
    if (styleData && dynamicData) {
      await migrateProjectSettings(projectId, styleData, dynamicData);
      status.migratedStores.push('style-store', 'dynamic-style-store');
    }

    // 迁移业务数据
    if (businessData) {
      await migrateBusinessData(projectId, businessData);
      status.migratedStores.push('business-data-store');
    }

    // 迁移组合数据
    if (combinationData) {
      await migrateCombinationData(projectId, combinationData);
      status.migratedStores.push('combination-data-storage');
    }
    
    status.isCompleted = true;
    status.lastMigrationDate = new Date();
    
    console.log('✅ 数据迁移完成!');
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '未知错误';
    status.errors.push(errorMessage);
    console.error('❌ 数据迁移失败:', error);
  }
  
  return status;
}

// 检查是否需要迁移
export function needsMigration(): boolean {
  if (typeof window === 'undefined') return false;
  
  const stores = [
    'basic-data-store',
    'style-store', 
    'dynamic-style-store',
    'business-data-store',
    'combination-data-storage'
  ];
  
  return stores.some(store => localStorage.getItem(store) !== null);
}
