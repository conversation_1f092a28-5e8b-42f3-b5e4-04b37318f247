# Vercel生产环境变量模板
# 🎯 复制此文件内容到Vercel环境变量设置中
# 🔄 替换所有 YOUR_* 占位符为实际值

# === 必需环境变量 ===

# 数据库连接 (Vercel PostgreSQL)
DATABASE_URL=YOUR_POSTGRESQL_CONNECTION_STRING

# 认证密钥 (生成随机字符串)
NEXTAUTH_SECRET=YOUR_SECURE_RANDOM_SECRET_KEY

# 应用URL (你的Vercel域名)
NEXTAUTH_URL=https://YOUR_VERCEL_DOMAIN.vercel.app

# === 应用配置 ===

# 环境标识
NODE_ENV=production
NEXT_PUBLIC_ENV=production

# API配置
NEXT_PUBLIC_API_URL=https://YOUR_VERCEL_DOMAIN.vercel.app/api

# === 功能开关 ===

# 启用API模式
NEXT_PUBLIC_ENABLE_API=true

# 禁用开发工具
NEXT_PUBLIC_ENABLE_DEV_TOOLS=false

# 禁用数据迁移 (生产环境)
NEXT_PUBLIC_ENABLE_MIGRATION=false

# === 性能配置 ===

# API超时时间 (生产环境较长)
NEXT_PUBLIC_API_TIMEOUT=15000

# 同步间隔 (生产环境较长)
NEXT_PUBLIC_SYNC_INTERVAL=60000

# === 可选配置 ===

# 日志级别
NEXT_PUBLIC_LOG_LEVEL=error

# 调试模式 (生产环境关闭)
NEXT_PUBLIC_DEBUG=false

# === Vercel特定配置 ===

# 构建命令会自动设置以下变量:
# VERCEL=1
# VERCEL_ENV=production
# VERCEL_URL=your-deployment-url
# VERCEL_GIT_COMMIT_SHA=commit-hash
# VERCEL_GIT_COMMIT_REF=branch-name

# === 设置说明 ===

# 1. DATABASE_URL 设置:
#    - 在Vercel中添加PostgreSQL数据库
#    - 复制连接字符串到此变量
#    - 格式: postgresql://username:password@host:port/database?schema=public

# 2. NEXTAUTH_SECRET 生成:
#    - 使用命令: openssl rand -base64 32
#    - 或访问: https://generate-secret.vercel.app/32

# 3. NEXTAUTH_URL 设置:
#    - 替换为你的实际Vercel域名
#    - 例如: https://cube1-group.vercel.app

# 4. 在Vercel中设置环境变量:
#    - 访问项目设置 > Environment Variables
#    - 添加上述所有变量
#    - 确保选择 "Production" 环境
