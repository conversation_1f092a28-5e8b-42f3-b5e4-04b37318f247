/**
 * 样式常量定义 - 静态样式配置集中管理
 * 🎯 职责：按钮样式、UI配置、主题配置等纯常量定义
 * 📦 数据源：原styleStore.ts中的样式常量
 * ✅ Phase 4.7.1: 职责分离 - 静态常量与状态管理分离
 */

// 按钮样式类型定义 - 定义按钮的变体类型
export type ButtonVariant = 'primary' | 'secondary' | 'active' | 'inactive' | 'danger' | 'success' | 'neutral';
// 按钮尺寸类型定义 - 定义按钮的尺寸类型
export type ButtonSize = 'xs' | 'sm' | 'md' | 'lg';

// 按钮尺寸样式 - 根据不同尺寸定义对应的CSS类
export const SIZE_STYLES: Record<ButtonSize, string> = {
  xs: 'px-2 py-1 text-xs',      // 超小尺寸：2px内边距、1px上下边距、超小文字
  sm: 'px-3 py-1.5 text-sm',    // 小尺寸：3px内边距、1.5px上下边距、小文字
  md: 'px-4 py-2 text-sm',      // 中等尺寸：4px内边距、2px上下边距、小文字
  lg: 'px-6 py-3 text-base',    // 大尺寸：6px内边距、3px上下边距、基础文字
};

// 按钮变体样式 - 根据不同变体定义对应的颜色和状态样式
export const VARIANT_STYLES: Record<ButtonVariant, string> = {
  primary: 'bg-blue-600 text-white border-blue-500 hover:bg-blue-700',        // 主要按钮：蓝色背景、白色文字
  secondary: 'bg-gray-100 text-gray-700 border-gray-200 hover:bg-gray-200',   // 次要按钮：灰色背景、深灰文字
  active: 'bg-gray-200 text-gray-800 border-gray-400',                        // 激活状态：浅灰背景、深灰文字
  inactive: 'bg-transparent text-gray-300 border-gray-600 hover:bg-gray-700', // 非激活状态：透明背景、浅灰文字
  danger: 'bg-red-600 text-white border-red-500 hover:bg-red-700',            // 危险按钮：红色背景、白色文字
  success: 'bg-green-600 text-white border-green-500 hover:bg-green-700',     // 成功按钮：绿色背景、白色文字
  neutral: 'bg-white text-gray-600 border-gray-200 hover:bg-gray-50',         // 中性按钮：白色背景、灰色文字
};

// 按钮基础样式 - 所有按钮共用的基础样式类
export const BASE_BUTTON_STYLES = 'rounded border transition-colors duration-200 flex items-center justify-center font-medium focus:outline-none focus:ring-2 focus:ring-offset-2';

// 按钮状态样式组合（保持向后兼容） - 旧版本的按钮样式配置
export const BUTTON_STYLES = {
  active: 'bg-gray-200 text-gray-800 border-gray-400',                    // 激活状态样式
  inactive: 'bg-white text-gray-600 border-gray-200 hover:bg-gray-50',    // 非激活状态样式
  primary: 'bg-gray-200 text-gray-800 border-gray-400 hover:bg-gray-300', // 主要状态样式
  secondary: 'bg-gray-100 text-gray-700 border-gray-200 hover:bg-gray-200', // 次要状态样式
} as const;

// Tab切换样式 - 标签页切换的样式配置
export const TAB_STYLES = {
  active: 'bg-white text-gray-800 border-gray-400 shadow-sm',         // 激活标签：白色背景、阴影效果
  inactive: 'bg-gray-100 text-gray-600 border-gray-200 hover:bg-gray-200', // 非激活标签：灰色背景、悬停效果
} as const;

// 输入框样式 - 表单输入框的样式配置
export const INPUT_STYLES = {
  default: 'px-2 py-1 text-xs border border-gray-200 rounded focus:border-gray-400 focus:outline-none', // 默认输入框样式
  center: 'text-center', // 文字居中样式
} as const;

// 网格布局样式 - CSS Grid布局的列数配置
export const GRID_STYLES = {
  cols2: 'grid-cols-2', // 2列网格
  cols3: 'grid-cols-3', // 3列网格
  cols4: 'grid-cols-4', // 4列网格
  cols5: 'grid-cols-5', // 5列网格
} as const;

// 默认样式配置 - 矩阵组件的默认样式
export const DEFAULT_MATRIX_STYLES = {
  container: 'flex-1 relative overflow-hidden bg-gray-100',     // 容器样式：弹性布局、相对定位、隐藏溢出、灰色背景
  gridLayout: 'grid gap-1 p-4',                                // 网格布局：Grid显示、1px间距、4px内边距
  cellBase: 'aspect-square flex items-center justify-center text-xs font-medium cursor-pointer border border-gray-300 transition-all duration-200', // 单元格基础样式
  cellHover: 'hover:scale-105 hover:z-10',                     // 单元格悬停样式：放大效果、提升层级
} as const;

// 默认控制面板样式 - 控制面板组件的样式配置
export const DEFAULT_CONTROL_PANEL_STYLES = {
  container: 'w-80 h-full bg-white border-l border-gray-200 flex flex-col shadow-lg', // 容器：固定宽度、全高、白色背景、左边框、弹性列布局、阴影
  header: 'p-4 border-b border-gray-200 bg-gray-50',         // 头部：内边距、下边框、浅灰背景
  content: 'flex-1 p-4 overflow-y-auto',                     // 内容区：弹性增长、内边距、垂直滚动
  footer: 'p-4 border-t border-gray-200 bg-gray-50',         // 底部：内边距、上边框、浅灰背景
} as const;

// 默认按钮样式 - 通用按钮组件的样式配置
export const DEFAULT_BUTTON_STYLES = {
  primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',   // 主要按钮：蓝色主题
  secondary: 'bg-gray-200 text-gray-700 hover:bg-gray-300 focus:ring-gray-500', // 次要按钮：灰色主题
  danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',       // 危险按钮：红色主题
  success: 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500', // 成功按钮：绿色主题
} as const;

// 默认颜色方案 - 应用的默认配色方案
export const DEFAULT_COLOR_SCHEME = {
  background: {
    primary: '#ffffff',   // 主要背景色：纯白色
    secondary: '#f8fafc', // 次要背景色：浅灰色
    tertiary: '#f1f5f9',  // 第三背景色：更浅的灰色
  },
  text: {
    primary: '#0f172a',   // 主要文字颜色：深灰近黑色
    secondary: '#475569', // 次要文字颜色：中等灰色
    accent: '#3b82f6',    // 强调文字颜色：蓝色
  },
  border: {
    primary: '#e2e8f0',   // 主要边框颜色：浅灰色
    secondary: '#cbd5e1', // 次要边框颜色：中等灰色
    focus: '#3b82f6',     // 焦点边框颜色：蓝色
  },
} as const;

// 样式接口定义 - 矩阵样式接口，定义矩阵组件所需的样式属性
export interface MatrixStyles {
  container: string;  // 容器样式
  gridLayout: string; // 网格布局样式
  cellBase: string;   // 单元格基础样式
  cellHover: string;  // 单元格悬停样式
}

// 控制面板样式接口 - 定义控制面板组件所需的样式属性
export interface ControlPanelStyles {
  container: string; // 容器样式
  header: string;    // 头部样式
  content: string;   // 内容区样式
  footer: string;    // 底部样式
}

// 按钮样式接口 - 定义按钮组件所需的样式属性
export interface ButtonStyles {
  primary: string;   // 主要按钮样式
  secondary: string; // 次要按钮样式
  danger: string;    // 危险按钮样式
  success: string;   // 成功按钮样式
}

// 颜色方案接口 - 定义应用配色方案的结构
export interface ColorScheme {
  background: {
    primary: string;   // 主要背景色
    secondary: string; // 次要背景色
    tertiary: string;  // 第三背景色
  };
  text: {
    primary: string;   // 主要文字颜色
    secondary: string; // 次要文字颜色
    accent: string;    // 强调文字颜色
  };
  border: {
    primary: string;   // 主要边框颜色
    secondary: string; // 次要边框颜色
    focus: string;     // 焦点边框颜色
  };
} 