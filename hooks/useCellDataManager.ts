/**
 * useCellDataManager Hook
 * 🎯 核心价值：在React组件中使用CellDataManager的统一接口
 * ⚡ 性能优化：自动订阅数据变更、memoization优化
 * 📊 功能范围：CRUD操作、查询、批量更新、事件监听
 * 🔄 架构设计：响应式数据管理、类型安全、错误处理
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import type { CellData } from '../types/grid';
import { 
  CellDataManager, 
  type DataChangeEvent, 
  type QueryConditions,
  type ColorType,
  type LevelType,
  type GroupType 
} from '../utils/CellDataManager';
import {
  initializeGridData,
  batchUpdateCellColors,
  batchUpdateCellGroups,
  getCellsByColorAndLevel,
  getGroupCellIds,
  validateCellData,
} from '../utils/cellDataHelpers';

// Hook返回类型定义
export interface UseCellDataManagerReturn {
  // 数据状态
  cells: CellData[];
  isLoading: boolean;
  error: string | null;
  stats: ReturnType<CellDataManager['getStats']>;

  // 基础CRUD操作
  createCell: (cellData: Omit<CellData, 'id'>) => CellData | null;
  getCellById: (id: string) => CellData | undefined;
  getCellByPosition: (row: number, column: number) => CellData | undefined;
  getCellByIndex: (index: number) => CellData | undefined;
  updateCell: (id: string, updates: Partial<CellData>) => boolean;
  deleteCell: (id: string) => boolean;

  // 批量操作
  batchUpdate: (updates: Array<{ id: string; data: Partial<CellData> }>) => boolean;
  batchUpdateColors: (cellIds: string[], color: ColorType, level?: LevelType) => boolean;
  batchUpdateGroups: (cellIds: string[], group: number) => boolean;

  // 查询操作
  queryCells: (conditions: QueryConditions) => CellData[];
  getCellsByGroup: (group: number) => CellData[];
  getCellsByColor: (color: string) => CellData[];
  getCellsByLevel: (level: LevelType) => CellData[];
  getCellsByColorAndLevel: (color: ColorType, level: LevelType) => CellData[];

  // 分组操作
  getGroupCellIds: (group: number) => string[];
  getCrossCells: () => CellData[]; // 十字组(1-10)
  getDiagonalCells: () => CellData[]; // 交叉组(11-44)

  // 工具函数
  initializeGrid: () => void;
  clearAll: () => void;
  refreshData: () => void;
  validateCell: (cell: Partial<CellData>) => string[];
}

/**
 * CellDataManager React Hook
 */
export function useCellDataManager(): UseCellDataManagerReturn {
  const [cells, setCells] = useState<CellData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState(0);

  // 获取CellDataManager实例
  const manager = useMemo(() => CellDataManager.getInstance(), []);

  // 数据变更处理
  const handleDataChange = useCallback((event: DataChangeEvent) => {
    setLastUpdate(Date.now());
    setError(null);
  }, []);

  // 刷新数据
  const refreshData = useCallback(() => {
    try {
      setIsLoading(true);
      const allCells = manager.getAllCells();
      setCells(allCells);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsLoading(false);
    }
  }, [manager]);

  // 初始化和清理
  useEffect(() => {
    // 添加数据变更监听器
    manager.addChangeListener(handleDataChange);
    
    // 初始化数据
    refreshData();

    return () => {
      // 清理监听器
      manager.removeChangeListener(handleDataChange);
    };
  }, [manager, handleDataChange, refreshData]);

  // 当数据变更时刷新
  useEffect(() => {
    if (lastUpdate > 0) {
      refreshData();
    }
  }, [lastUpdate, refreshData]);

  // 基础CRUD操作
  const createCell = useCallback((cellData: Omit<CellData, 'id'>): CellData | null => {
    try {
      const errors = validateCellData(cellData);
      if (errors.length > 0) {
        setError(`Validation errors: ${errors.join(', ')}`);
        return null;
      }
      return manager.createCell(cellData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create cell');
      return null;
    }
  }, [manager]);

  const getCellById = useCallback((id: string) => {
    return manager.getCellById(id);
  }, [manager]);

  const getCellByPosition = useCallback((row: number, column: number) => {
    return manager.getCellByPosition(row, column);
  }, [manager]);

  const getCellByIndex = useCallback((index: number) => {
    return manager.getCellByIndex(index);
  }, [manager]);

  const updateCell = useCallback((id: string, updates: Partial<CellData>): boolean => {
    try {
      const errors = validateCellData(updates);
      if (errors.length > 0) {
        setError(`Validation errors: ${errors.join(', ')}`);
        return false;
      }
      return manager.updateCell(id, updates);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update cell');
      return false;
    }
  }, [manager]);

  const deleteCell = useCallback((id: string): boolean => {
    try {
      return manager.deleteCell(id);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete cell');
      return false;
    }
  }, [manager]);

  // 批量操作
  const batchUpdate = useCallback((updates: Array<{ id: string; data: Partial<CellData> }>): boolean => {
    try {
      // 验证所有更新数据
      for (const update of updates) {
        const errors = validateCellData(update.data);
        if (errors.length > 0) {
          setError(`Validation errors for ${update.id}: ${errors.join(', ')}`);
          return false;
        }
      }
      return manager.batchUpdate(updates);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to batch update');
      return false;
    }
  }, [manager]);

  const batchUpdateColors = useCallback((cellIds: string[], color: ColorType, level?: LevelType): boolean => {
    try {
      return batchUpdateCellColors(manager, cellIds, color, level);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to batch update colors');
      return false;
    }
  }, [manager]);

  const batchUpdateGroups = useCallback((cellIds: string[], group: number): boolean => {
    try {
      return batchUpdateCellGroups(manager, cellIds, group);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to batch update groups');
      return false;
    }
  }, [manager]);

  // 查询操作
  const queryCells = useCallback((conditions: QueryConditions) => {
    return manager.queryCells(conditions);
  }, [manager]);

  const getCellsByGroup = useCallback((group: number) => {
    return manager.getCellsByGroup(group);
  }, [manager]);

  const getCellsByColor = useCallback((color: string) => {
    return manager.getCellsByColor(color);
  }, [manager]);

  const getCellsByLevel = useCallback((level: LevelType) => {
    return manager.getCellsByLevel(level);
  }, [manager]);

  const getCellsByColorAndLevelMemo = useCallback((color: ColorType, level: LevelType) => {
    return getCellsByColorAndLevel(manager, color, level);
  }, [manager]);

  // 分组操作
  const getGroupCellIdsMemo = useCallback((group: number) => {
    return getGroupCellIds(manager, group);
  }, [manager]);

  const getCrossCells = useCallback(() => {
    return manager.queryCells({ groupType: 'cross' });
  }, [manager]);

  const getDiagonalCells = useCallback(() => {
    return manager.queryCells({ groupType: 'diagonal' });
  }, [manager]);

  // 工具函数
  const initializeGrid = useCallback(() => {
    try {
      setIsLoading(true);
      initializeGridData(manager);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to initialize grid');
    } finally {
      setIsLoading(false);
    }
  }, [manager]);

  const clearAll = useCallback(() => {
    try {
      manager.clear();
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to clear data');
    }
  }, [manager]);

  const validateCell = useCallback((cell: Partial<CellData>) => {
    return validateCellData(cell);
  }, []);

  // 统计信息
  const stats = useMemo(() => {
    return manager.getStats();
  }, [manager, lastUpdate]);

  return {
    // 数据状态
    cells,
    isLoading,
    error,
    stats,

    // 基础CRUD操作
    createCell,
    getCellById,
    getCellByPosition,
    getCellByIndex,
    updateCell,
    deleteCell,

    // 批量操作
    batchUpdate,
    batchUpdateColors,
    batchUpdateGroups,

    // 查询操作
    queryCells,
    getCellsByGroup,
    getCellsByColor,
    getCellsByLevel,
    getCellsByColorAndLevel: getCellsByColorAndLevelMemo,

    // 分组操作
    getGroupCellIds: getGroupCellIdsMemo,
    getCrossCells,
    getDiagonalCells,

    // 工具函数
    initializeGrid,
    clearAll,
    refreshData,
    validateCell,
  };
}
