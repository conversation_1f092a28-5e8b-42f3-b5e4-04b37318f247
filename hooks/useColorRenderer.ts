/**
 * useColorRenderer Hook
 * 🎯 核心价值：在React组件中使用ColorRenderer的统一接口
 * ⚡ 性能优化：memoization、批量计算、缓存管理
 * 📊 功能范围：样式计算、颜色配置、渲染优化
 * 🔄 架构设计：响应式颜色管理、类型安全
 */

import { useState, useCallback, useMemo, useEffect } from 'react';
import type { CellData } from '../types/grid';
import { 
  ColorRenderer, 
  type ColorRenderConfig, 
  type CellStyle 
} from '../utils/colorRenderer';
import { type ColorType } from '../utils/CellDataManager';

// Hook配置选项
export interface UseColorRendererOptions {
  cellShape?: 'square' | 'rounded' | 'circle';
  fontSize?: number;
  enableHover?: boolean;
  enableBatchOptimization?: boolean;
}

// Hook返回类型
export interface UseColorRendererReturn {
  // 样式计算
  calculateCellStyle: (cell: CellData, isSelected?: boolean) => CellStyle;
  calculateCellContent: (cell: CellData, displayMode?: 'number' | 'coordinate' | 'hidden') => string;
  batchCalculateStyles: (cells: CellData[]) => Map<string, CellStyle>;

  // 颜色配置
  getColorConfig: (color: ColorType) => ColorRenderConfig;
  setColorConfig: (color: ColorType, config: Partial<ColorRenderConfig>) => void;
  resetColorConfig: (color: ColorType) => void;
  resetAllConfigs: () => void;

  // 颜色工具
  getEffectiveColor: (cell: CellData) => string;
  getAdjustedColor: (color: string, brightness?: number) => string;

  // 可见性控制
  toggleColorVisibility: (color: ColorType) => void;
  toggleLevelVisibility: (color: ColorType, level: 1 | 2 | 3 | 4) => void;
  setColorOpacity: (color: ColorType, opacity: number) => void;
  setColorBrightness: (color: ColorType, brightness: number) => void;

  // 批量操作
  showAllColors: () => void;
  hideAllColors: () => void;
  showAllLevels: (color: ColorType) => void;
  hideAllLevels: (color: ColorType) => void;

  // 缓存管理
  clearCache: () => void;
  getCacheStats: () => ReturnType<ColorRenderer['getCacheStats']>;

  // 配置状态
  configs: Map<ColorType, ColorRenderConfig>;
  isLoading: boolean;
}

/**
 * ColorRenderer React Hook
 */
export function useColorRenderer(options: UseColorRendererOptions = {}): UseColorRendererReturn {
  const {
    cellShape = 'square',
    fontSize = 12,
    enableHover = false,
    enableBatchOptimization = true,
  } = options;

  const [configs, setConfigs] = useState<Map<ColorType, ColorRenderConfig>>(new Map());
  const [isLoading, setIsLoading] = useState(false);

  // 获取ColorRenderer实例
  const renderer = useMemo(() => ColorRenderer.getInstance(), []);

  // 初始化配置
  useEffect(() => {
    const initialConfigs = new Map<ColorType, ColorRenderConfig>();
    const colorTypes: ColorType[] = ['red', 'cyan', 'yellow', 'purple', 'orange', 'green', 'blue', 'pink'];
    
    colorTypes.forEach(color => {
      initialConfigs.set(color, renderer.getColorConfig(color));
    });
    
    setConfigs(initialConfigs);
  }, [renderer]);

  // 样式计算函数
  const calculateCellStyle = useCallback((cell: CellData, isSelected: boolean = false): CellStyle => {
    return renderer.calculateCellStyle(cell, {
      cellShape,
      fontSize,
      enableHover,
      isSelected,
    });
  }, [renderer, cellShape, fontSize, enableHover]);

  const calculateCellContent = useCallback((
    cell: CellData, 
    displayMode: 'number' | 'coordinate' | 'hidden' = 'number'
  ): string => {
    return renderer.calculateCellContent(cell, displayMode);
  }, [renderer]);

  const batchCalculateStyles = useCallback((cells: CellData[]): Map<string, CellStyle> => {
    if (!enableBatchOptimization) {
      const results = new Map<string, CellStyle>();
      cells.forEach(cell => {
        results.set(cell.id, calculateCellStyle(cell));
      });
      return results;
    }

    return renderer.batchCalculateStyles(cells, {
      cellShape,
      fontSize,
      enableHover,
    });
  }, [renderer, enableBatchOptimization, calculateCellStyle, cellShape, fontSize, enableHover]);

  // 颜色配置函数
  const getColorConfig = useCallback((color: ColorType): ColorRenderConfig => {
    return configs.get(color) || renderer.getColorConfig(color);
  }, [configs, renderer]);

  const setColorConfig = useCallback((color: ColorType, config: Partial<ColorRenderConfig>): void => {
    renderer.setColorConfig(color, config);
    const updatedConfig = renderer.getColorConfig(color);
    setConfigs(prev => new Map(prev).set(color, updatedConfig));
  }, [renderer]);

  const resetColorConfig = useCallback((color: ColorType): void => {
    const defaultConfig: ColorRenderConfig = {
      showCells: true,
      showLevel1: true,
      showLevel2: true,
      showLevel3: true,
      showLevel4: true,
      opacity: 1.0,
      brightness: 1.0,
    };
    setColorConfig(color, defaultConfig);
  }, [setColorConfig]);

  const resetAllConfigs = useCallback((): void => {
    const colorTypes: ColorType[] = ['red', 'cyan', 'yellow', 'purple', 'orange', 'green', 'blue', 'pink'];
    colorTypes.forEach(color => resetColorConfig(color));
  }, [resetColorConfig]);

  // 颜色工具函数
  const getEffectiveColor = useCallback((cell: CellData): string => {
    return renderer.getEffectiveColor(cell);
  }, [renderer]);

  const getAdjustedColor = useCallback((color: string, brightness: number = 1.0): string => {
    return renderer.getAdjustedColor(color, brightness);
  }, [renderer]);

  // 可见性控制函数
  const toggleColorVisibility = useCallback((color: ColorType): void => {
    const currentConfig = getColorConfig(color);
    setColorConfig(color, { showCells: !currentConfig.showCells });
  }, [getColorConfig, setColorConfig]);

  const toggleLevelVisibility = useCallback((color: ColorType, level: 1 | 2 | 3 | 4): void => {
    const currentConfig = getColorConfig(color);
    const levelKey = `showLevel${level}` as keyof ColorRenderConfig;
    setColorConfig(color, { [levelKey]: !currentConfig[levelKey] });
  }, [getColorConfig, setColorConfig]);

  const setColorOpacity = useCallback((color: ColorType, opacity: number): void => {
    const clampedOpacity = Math.max(0, Math.min(1, opacity));
    setColorConfig(color, { opacity: clampedOpacity });
  }, [setColorConfig]);

  const setColorBrightness = useCallback((color: ColorType, brightness: number): void => {
    const clampedBrightness = Math.max(0, Math.min(2, brightness));
    setColorConfig(color, { brightness: clampedBrightness });
  }, [setColorConfig]);

  // 批量操作函数
  const showAllColors = useCallback((): void => {
    const colorTypes: ColorType[] = ['red', 'cyan', 'yellow', 'purple', 'orange', 'green', 'blue', 'pink'];
    colorTypes.forEach(color => {
      setColorConfig(color, { showCells: true });
    });
  }, [setColorConfig]);

  const hideAllColors = useCallback((): void => {
    const colorTypes: ColorType[] = ['red', 'cyan', 'yellow', 'purple', 'orange', 'green', 'blue', 'pink'];
    colorTypes.forEach(color => {
      setColorConfig(color, { showCells: false });
    });
  }, [setColorConfig]);

  const showAllLevels = useCallback((color: ColorType): void => {
    setColorConfig(color, {
      showLevel1: true,
      showLevel2: true,
      showLevel3: true,
      showLevel4: true,
    });
  }, [setColorConfig]);

  const hideAllLevels = useCallback((color: ColorType): void => {
    setColorConfig(color, {
      showLevel1: false,
      showLevel2: false,
      showLevel3: false,
      showLevel4: false,
    });
  }, [setColorConfig]);

  // 缓存管理函数
  const clearCache = useCallback((): void => {
    renderer.clearStyleCache();
  }, [renderer]);

  const getCacheStats = useCallback(() => {
    return renderer.getCacheStats();
  }, [renderer]);

  return {
    // 样式计算
    calculateCellStyle,
    calculateCellContent,
    batchCalculateStyles,

    // 颜色配置
    getColorConfig,
    setColorConfig,
    resetColorConfig,
    resetAllConfigs,

    // 颜色工具
    getEffectiveColor,
    getAdjustedColor,

    // 可见性控制
    toggleColorVisibility,
    toggleLevelVisibility,
    setColorOpacity,
    setColorBrightness,

    // 批量操作
    showAllColors,
    hideAllColors,
    showAllLevels,
    hideAllLevels,

    // 缓存管理
    clearCache,
    getCacheStats,

    // 配置状态
    configs,
    isLoading,
  };
}

/**
 * 预设配置Hook - 提供常用的颜色配置预设
 */
export function useColorPresets() {
  const { setColorConfig, resetAllConfigs } = useColorRenderer();

  const applyHighContrastPreset = useCallback(() => {
    const colorTypes: ColorType[] = ['red', 'cyan', 'yellow', 'purple', 'orange', 'green', 'blue', 'pink'];
    colorTypes.forEach(color => {
      setColorConfig(color, {
        brightness: 1.2,
        opacity: 1.0,
      });
    });
  }, [setColorConfig]);

  const applyLowContrastPreset = useCallback(() => {
    const colorTypes: ColorType[] = ['red', 'cyan', 'yellow', 'purple', 'orange', 'green', 'blue', 'pink'];
    colorTypes.forEach(color => {
      setColorConfig(color, {
        brightness: 0.8,
        opacity: 0.7,
      });
    });
  }, [setColorConfig]);

  const applyGrayscalePreset = useCallback(() => {
    const colorTypes: ColorType[] = ['red', 'cyan', 'yellow', 'purple', 'orange', 'green', 'blue', 'pink'];
    colorTypes.forEach(color => {
      setColorConfig(color, {
        brightness: 0.5,
        opacity: 0.8,
      });
    });
  }, [setColorConfig]);

  return {
    applyHighContrastPreset,
    applyLowContrastPreset,
    applyGrayscalePreset,
    resetToDefault: resetAllConfigs,
  };
}
