/**
 * BasicDataStore API增强版 - 临时简化版
 * 🚧 状态：由于数据结构不匹配问题，临时简化此文件
 * 🔄 TODO：重构API数据结构以匹配新的CellData架构
 */

// 临时导出原始store以保持兼容性
export {
  useBasicDataStore as useApiEnhancedBasicDataStore,
  useColorCoordinates as useApiColorCoordinates,
  useColorValues as useApiColorValues,
  type BasicColorType,
  type ColorValue,
  type ColorVisibility
} from './basicDataStore';

// 临时的API状态选择器
export const useApiSyncStatus = () => ({
  isOnline: false,
  isSyncing: false,
  lastSyncTime: null,
  syncErrors: []
});

// 文件结束 - 其余代码已被注释掉等待重构