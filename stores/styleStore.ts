/**
 * 样式Store - 管理UI样式主题和显示状态
 * 🎯 核心职责：主题管理、黑色格子显示控制、状态管理
 * ✅ Phase 4.7.1: 职责分离完成 - 只保留状态管理，常量和工具函数已分离
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// 导入分离的常量和工具函数
import {
  ColorType,
  BasicColorType,
} from '../constants/colors';
import {
  ButtonVariant,
  ButtonSize,
  MatrixStyles,
  ControlPanelStyles,
  ButtonStyles,
  ColorScheme,
  DEFAULT_MATRIX_STYLES,
  DEFAULT_CONTROL_PANEL_STYLES,
  DEFAULT_BUTTON_STYLES,
  DEFAULT_COLOR_SCHEME,
} from '../constants/styles';
import {
  getColorCSS,
  getColorCSSMap,
  getBlackCSS,
  getColorName,
  getColorDisplayName,
  getColorNumber,
  getColorPriority,
} from '../utils/colorUtils';
import {
  getButtonStyle,
  getTabStyle,
  getGridStyle,
  getAdvancedButtonStyle,
  getActiveButtonStyle,
  getColorButtonStyle,
  getGridButtonStyle,
  getModeButtonStyle,
  getDangerButtonStyle,
  getSuccessButtonStyle,
  getButtonStylePlaceholder,
} from '../utils/buttonUtils';

export interface StyleState {
  // 基础样式配置
  matrixStyles: MatrixStyles;
  controlPanelStyles: ControlPanelStyles;
  buttonStyles: ButtonStyles;
  colorScheme: ColorScheme;
  
  // 主题设置
  currentTheme: 'light' | 'dark';
  
  // 特殊显示控制
  showBlackCells: boolean;
  
  // 样式操作
  setMatrixStyle: (style: Partial<MatrixStyles>) => void;
  setControlPanelTheme: (theme: 'light' | 'dark') => void;
  updateColorScheme: (scheme: Partial<ColorScheme>) => void;
  setCurrentTheme: (theme: 'light' | 'dark') => void;
  
  // 特殊显示控制操作
  setShowBlackCells: (show: boolean) => void;
  toggleBlackCells: () => void;
  
  // CSS映射访问器（代理到工具函数）
  getColorCSS: (colorType: ColorType, styleType: string) => string;
  getColorCSSMap: (colorType: ColorType) => typeof import('../constants/colors').COLOR_CSS_MAP[ColorType] | undefined;
  getBlackCSS: (styleType: string) => string;
  getColorName: (colorType: ColorType | 'black') => string;
  getButtonStyle: (variant: keyof typeof import('../constants/styles').BUTTON_STYLES) => string;
  getTabStyle: (active: boolean) => string;
  getGridStyle: (cols: 2 | 3 | 4 | 5) => string;
  
  // 颜色工具函数（代理到工具函数）
  getColorDisplayName: (colorType: BasicColorType, format?: 'full' | 'short') => string;
  getColorNumber: (colorType: Exclude<BasicColorType, 'black'>) => string;
  getColorPriority: (colorType: BasicColorType) => number;
  
  // 按钮样式工具函数（代理到工具函数）
  getAdvancedButtonStyle: (variant: ButtonVariant, size: ButtonSize, extraClasses?: string) => string;
  getActiveButtonStyle: (isActive: boolean, size?: ButtonSize, extraClasses?: string) => string;
  getColorButtonStyle: (isSelected: boolean, colorClass?: string, size?: ButtonSize) => string;
  getGridButtonStyle: (isSelected: boolean, color?: BasicColorType | 'gray', size?: ButtonSize) => string;
  getModeButtonStyle: (isCurrentMode: boolean, size?: ButtonSize) => string;
  getDangerButtonStyle: (size?: ButtonSize, extraClasses?: string) => string;
  getSuccessButtonStyle: (size?: ButtonSize, extraClasses?: string) => string;
  
  // 主题重置
  resetStyles: () => void;
}

export const useStyleStore = create<StyleState>()(
  persist(
    (set, get) => ({
      // 初始状态
      matrixStyles: DEFAULT_MATRIX_STYLES,
      controlPanelStyles: DEFAULT_CONTROL_PANEL_STYLES,
      buttonStyles: DEFAULT_BUTTON_STYLES,
      colorScheme: DEFAULT_COLOR_SCHEME,
      currentTheme: 'light',
      showBlackCells: true,

      // 样式操作
      setMatrixStyle: (style) =>
        set((state) => ({
          matrixStyles: { ...state.matrixStyles, ...style },
        })),

      setControlPanelTheme: (theme) =>
        set((state) => {
          const isDark = theme === 'dark';
          return {
            controlPanelStyles: {
              ...state.controlPanelStyles,
              container: isDark 
                ? 'w-80 h-full bg-gray-800 border-l border-gray-700 flex flex-col'
                : DEFAULT_CONTROL_PANEL_STYLES.container,
              header: isDark
                ? 'p-4 border-b border-gray-700 bg-gray-700'
                : DEFAULT_CONTROL_PANEL_STYLES.header,
              content: isDark
                ? 'flex-1 p-4 overflow-y-auto text-white'
                : DEFAULT_CONTROL_PANEL_STYLES.content,
              footer: isDark
                ? 'p-4 border-t border-gray-700 bg-gray-700'
                : DEFAULT_CONTROL_PANEL_STYLES.footer,
            },
          };
        }),

      updateColorScheme: (scheme) =>
        set((state) => ({
          colorScheme: {
            background: { ...state.colorScheme.background, ...scheme.background },
            text: { ...state.colorScheme.text, ...scheme.text },
            border: { ...state.colorScheme.border, ...scheme.border },
          },
        })),

      setCurrentTheme: (theme) =>
        set(() => ({ currentTheme: theme })),

      // 特殊显示控制操作
      setShowBlackCells: (show) => set({ showBlackCells: show }),
      
      toggleBlackCells: () => 
        set((state) => ({ showBlackCells: !state.showBlackCells })),

      // CSS映射访问器（代理到工具函数）
      getColorCSS,
      getColorCSSMap,
      getBlackCSS,
      getColorName,
      getButtonStyle,
      getTabStyle,
      getGridStyle,

      // 颜色工具函数（代理到工具函数）
      getColorDisplayName,
      getColorNumber,
      getColorPriority,
      
      // 按钮样式工具函数（代理到工具函数）
      getAdvancedButtonStyle,
      getActiveButtonStyle,
      getColorButtonStyle,
      getGridButtonStyle,
      getModeButtonStyle,
      getDangerButtonStyle,
      getSuccessButtonStyle,

      // 主题重置
      resetStyles: () =>
        set(() => ({
          matrixStyles: DEFAULT_MATRIX_STYLES,
          controlPanelStyles: DEFAULT_CONTROL_PANEL_STYLES,
          buttonStyles: DEFAULT_BUTTON_STYLES,
          colorScheme: DEFAULT_COLOR_SCHEME,
          currentTheme: 'light',
          showBlackCells: true,
        })),
    }),
    {
      name: 'style-store',
      version: 3, // 版本升级，职责分离架构
    }
  )
);

// 选择器函数
export const useMatrixStyles = () => useStyleStore((state) => state.matrixStyles);
export const useControlPanelStyles = () => useStyleStore((state) => state.controlPanelStyles);
export const useButtonStyles = () => useStyleStore((state) => state.buttonStyles);
export const useColorScheme = () => useStyleStore((state) => state.colorScheme);
export const useCurrentTheme = () => useStyleStore((state) => state.currentTheme);
export const useShowBlackCells = () => useStyleStore((state) => state.showBlackCells);

// 工具函数（代理到utils）
export { getCellStyle, getTabStyle as getTabStyleUtil } from '../utils/colorUtils'; 