# 前端开发规范

**更新时间**: 2025年7月4日 09:16
**更新规则**：不更改结构，只有【1.技术栈】和【2.架构】，只更新内部内容

---

## 1. 技术栈

项目基于以下技术栈构建，已完成现代化全栈重构：

-   **框架**: [Next.js](mdc:https:/nextjs.org) (v14.2.23, App Router)
-   **语言**: [TypeScript](mdc:https:/www.typescriptlang.org) (v5, 严格模式)
-   **UI 库**: [React](mdc:https:/react.dev) (v18)
-   **样式**: [Tailwind CSS](mdc:https:/tailwindcss.com) (v3.4.1)
-   **UI 工具**: [Radix UI](mdc:https:/www.radix-ui.com), [Lucide React](mdc:https:/lucide.dev/guide/react) (v0.438.0)
-   **状态管理**: [Zustand](mdc:https:/github.com/pmndrs/zustand) (v5.0.5)
-   **工具函数**: [clsx](mdc:https:/github.com/lukeed/clsx) (v2.1.1), [tailwind-merge](mdc:https:/github.com/dcastil/tailwind-merge) (v2.5.2)
-   **代码质量**: [ESLint](mdc:https:/eslint.org), [Prettier](mdc:https:/prettier.io) (v3.3.3)
-   **测试框架**: [Jest](mdc:https:/jestjs.io) (v29.7.0), [ts-jest](mdc:https:/github.com/kulshekhar/ts-jest) (v29.4.0)
-   **构建工具**: [PostCSS](mdc:https:/postcss.org) (v8), [Tailwind CSS Animate](mdc:https:/github.com/jamiebuilds/tailwindcss-animate) (v1.0.7)
-   **🆕 后端技术**: [Prisma ORM](mdc:https:/prisma.io) (v6.10.1), [SQLite](开发)/[PostgreSQL](生产)
-   **🆕 部署**: [Vercel](mdc:https:/vercel.com) 全栈部署
-   **🆕 数据库**: [pg](v8.16.3), [sqlite3](v5.1.7)

---

## 2. 架构

### 2.1 结构现状
```
app/                 # Next.js App Router (1,067行) - 全栈架构
├── page.tsx          # 主应用文件 (82行) - 使用usePageLogic统一逻辑
├── layout.tsx        # 全局布局 (30行)
├── globals.css       # 全局样式 (128行)
├── favicon.ico       # 网站图标
└── api/             # 🆕 API路由系统 (955行)
    ├── health/       # 健康检查API (34行)
    ├── users/        # 用户管理API (293行)
    ├── projects/     # 项目管理API (557行)
    └── migration/    # 数据迁移API (71行)

components/          # 组件化架构 (2,716行) - 全面memo优化
├── Grid/            # 网格组件系统 (555行) - 虚拟滚动支持
│   ├── GridContainer.tsx    # 主容器组件 (258行) - Phase 6.1性能优化
│   ├── GridCell.tsx         # 单元格组件 (211行) - memo+useMemo优化
│   ├── GridOverlay.tsx      # 覆盖层组件 (26行)
│   ├── types.ts             # 类型定义 (55行)
│   └── index.ts             # 统一导出 (5行)
├── ControlPanel/    # 控制面板组件系统 (2,025行) - R2架构升级
│   ├── ControlPanelContainer.tsx    # 主容器 (113行) - R2新架构支持
│   ├── StylePanel.tsx               # R2样式面板 (254行)
│   ├── BasicDataPanel.tsx           # R2基础数据面板 (397行)
│   ├── CombinationBusinessPanel.tsx # R2组合业务面板 (490行)
│   ├── ColorSystemPanel.tsx         # 统一颜色面板 (171行)
│   ├── VersionPanel.tsx             # 版本管理 (138行)
│   ├── ColorLevelToggle.tsx         # 级别切换 (85行)
│   ├── ColorGroupSelector.tsx       # 分组选择 (100行)
│   ├── types.ts                     # 类型定义 (77行)
│   └── index.ts                     # 统一导出 (25行)
└── DevTools/        # 🆕 开发工具 (136行) - API测试和调试
    ├── DevPanel.tsx          # 开发面板 (271行)
    └── index.tsx             # 工具入口 (51行)

stores/              # Zustand状态管理 (4,057行) - 5Store架构
├── styleStore.ts                    # 样式状态 (206行) - CSS映射管理
├── dynamicStyleStore.ts             # 动态样式状态 (240行) - UI配置管理
├── basicDataStore.ts                # 基础数据状态 (1,748行) - 颜色坐标核心
├── combinationDataStore.ts          # 组合数据状态 (570行) - 组合逻辑管理
├── businessDataStore.ts             # 业务数据状态 (845行) - 交互&版本管理
├── basicDataStoreApi.ts             # 🆕 API增强基础数据 (299行) - 云端同步
└── index.ts                         # 统一导出 (149行) - 架构集成

hooks/               # 自定义Hook (1,350行) - 性能优化核心
├── usePageLogic.ts                  # 页面逻辑钩子 (522行) - 统一业务逻辑
├── useFormHandlers.ts               # 表单处理钩子 (604行) - 性能优化工具
└── useHybridDataStore.ts            # 🆕 混合数据存储 (224行) - 离线+在线

utils/               # 工具函数模块 (2,445行) - 高性能工具集
├── colorSystem.ts                   # 颜色系统工具 (199行) - ColorCoordinateIndex核心
├── dataConsistencyChecker.ts        # 数据一致性检查 (413行)
├── debugHelper.ts                   # 调试工具 (1,091行) - Debug Phase支持
├── groupLogicAnalyzer.ts            # 组合逻辑分析 (325行)
├── debugBlackCells.ts               # 黑色格子调试 (56行)
├── cellUtils.ts                     # 单元格工具 (56行)
├── styleUtils.ts                    # 样式工具 (56行)
├── buttonUtils.ts                   # 按钮工具 (56行)
└── colorUtils.ts                    # 颜色工具函数 (148行)

types/               # TypeScript类型定义 (457行)
├── api.ts                           # 🆕 API类型 (272行) - 全栈类型定义
├── version.ts                       # 版本类型 (108行)
├── color.ts                         # 颜色类型 (52行)
└── grid.ts                          # 网格类型 (25行)

lib/                 # 🆕 核心库文件 (1,314行)
├── api-client.ts                    # API客户端 (305行) - 前端API调用
├── api-utils.ts                     # API工具函数 (274行) - 后端API工具
├── prisma.ts                        # Prisma客户端 (57行) - 数据库连接
├── migration.ts                     # 数据迁移 (386行) - LocalStorage→DB
├── migration-test.ts                # 迁移测试 (286行) - 测试工具
└── utils.ts                         # 基础工具函数 (6行) - cn等

constants/           # 常量管理系统 (305行) - 分离架构
├── colors.ts                        # 颜色常量 (159行) - COLOR_CSS_MAP等
└── styles.ts                        # 样式常量 (146行) - 按钮、面板样式

prisma/              # 🆕 数据库层 (221行)
├── schema.prisma                    # 数据库模式 (221行) - 完整数据模型
├── seed.ts                          # 种子数据
└── dev.db                           # SQLite数据库文件

scripts/             # 🆕 自动化脚本 (1,200行)
├── quick-start.js                   # 快速启动 (300行)
├── dev-setup.js                     # 开发环境设置 (206行)
├── test-integration.js              # 集成测试 (365行)
├── code-quality-monitor.js          # 代码质量监控 (461行)
├── setup-env.js                     # 环境配置 (118行)
└── build-production.js              # 生产构建 (150行)

__tests__/           # 测试文件 (350行)
├── colorLevelDisplay.test.ts        # 颜色级别显示测试 (313行)
└── debug_phase4_report.md           # 调试报告 (37行)

docs/                # 🆕 项目文档 (2,000行)
├── report/                          # 分析报告
│   ├── project-comprehensive-guide.md    # 项目综合指南 (320行)
│   ├── code-analysis-report.md           # 代码分析报告 (318行)
│   ├── coding-standards.md               # 编码规范 (240行)
│   ├── implementation-roadmap.md         # 实施路线图 (280行)
│   ├── migration-summary.md              # 迁移总结 (150行)
│   ├── deployment.md                     # 部署指南 (200行)
│   └── README-ANALYSIS.md                # README分析 (180行)
└── log250627/                       # 开发日志
    └── [多个日志文件]                      # 开发过程记录
```

### 2.2 组件拆分原则
1. **单一职责**: 每个组件只负责一个功能域
2. **行数限制**: 组件文件<500行，函数<100行
3. **类型安全**: 所有组件必须有完整的TypeScript接口
4. **性能优化**: 
   - 🚀 全面使用React.memo包装组件 (15个组件已优化)
   - ⚡ 大量使用useMemo和useCallback进行性能优化
   - 🎯 避免useState，统一使用Zustand状态管理
   - 🔄 虚拟滚动支持大网格渲染（GridContainer）
   - 🆕 API增强存储支持离线+在线混合模式

### 2.3 文件组织规范
```
📁 根据功能域组织文件
├── app/           # Next.js App Router + API Routes - 全栈架构
├── components/    # 按功能分组的组件 - memo优化 (2,716行)
├── stores/        # 按业务域分离的状态 - 5Store架构 (4,057行)
├── hooks/         # 自定义Hook - 性能优化核心 (1,350行)
├── utils/         # 工具函数（ColorCoordinateIndex等高性能工具）(2,445行)
├── types/         # 类型定义 - 全栈类型支持 (457行)
├── lib/           # 🆕 核心库 - API客户端+数据库 (386行)
├── constants/     # 常量管理（分离架构）(305行)
├── prisma/        # 🆕 数据库层 (230行)
├── scripts/       # 🆕 自动化脚本 (1,200行)
├── __tests__/     # 测试文件 (350行)
└── docs/          # 🆕 项目文档 (2,000行)

🎯 每个目录都有index.ts统一导出
📊 代码总行数约32,041行（排除node_modules）
📈 TypeScript文件数量：131个
```

### 2.4 关键文件
- [app/page.tsx](mdc:app/page.tsx) - 主页面组件 (82行) - 使用usePageLogic
- [hooks/usePageLogic.ts](mdc:hooks/usePageLogic.ts) - 页面逻辑Hook (522行) - 业务逻辑中枢
- [utils/colorSystem.ts](mdc:utils/colorSystem.ts) - 性能优化核心 (199行) - ColorCoordinateIndex
- [components/Grid/GridContainer.tsx](mdc:components/Grid/GridContainer.tsx) - 网格组件 (258行) - 虚拟滚动
- [components/ControlPanel/ControlPanelContainer.tsx](mdc:components/ControlPanel/ControlPanelContainer.tsx) - R2架构 (113行)
- [stores/basicDataStore.ts](mdc:stores/basicDataStore.ts) - 基础数据核心 (1,748行)
- [utils/debugHelper.ts](mdc:utils/debugHelper.ts) - Debug Phase支持 (1,091行)
- [lib/api-client.ts](mdc:lib/api-client.ts) - 🆕 API客户端 (238行) - 前后端连接桥梁
- [prisma/schema.prisma](mdc:prisma/schema.prisma) - 🆕 数据库模式 (200行) - 完整数据模型
- [components/DevTools/DevPanel.tsx](mdc:components/DevTools/DevPanel.tsx) - 🆕 开发工具 (271行) - API测试调试

### 2.5 🆕 全栈架构特色
- **混合存储**: LocalStorage + API双重存储，支持离线模式
- **数据迁移**: 一键从本地数据迁移到云端数据库
- **开发工具**: 内置API测试面板（Ctrl+Shift+D）
- **自动化脚本**: 完整的开发、测试、部署工具链
- **类型安全**: 前后端共享TypeScript类型定义
- **RESTful API**: 完整的用户、项目、数据管理API
- **实时同步**: 支持多设备数据同步
- **健康监控**: API健康检查和错误处理



- [stores/basicDataStore.ts](mdc:stores/basicDataStore.ts) - 基础数据核心 (1,748行)
- [utils/debugHelper.ts](mdc:utils/debugHelper.ts) - Debug Phase支持 (529行)


