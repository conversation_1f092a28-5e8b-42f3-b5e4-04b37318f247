export interface CellData {
  id: string;                    // 单元格唯一标识符 (string)
  row: number;                   // 网格行位置 (0-based索引)
  column: number;                // 网格列位置 (0-based索引)
  x: number;                     // 画布X轴坐标 (像素值)
  y: number;                     // 画布Y轴坐标 (像素值)
  index: number;                 // 单元格序列编号 (全局唯一)
  color: string;                 // 当前显示颜色值 (十六进制格式如#FF0000)
  colorMappingValue: number;     // 用于颜色计算的数值 (包括黑色映射字符的统一处理)
  level: number;                 // 单元格层级 (1-based)
  group: number;                 // 单元格所属组别

  // 保持向后兼容的字段
  number: number;                // 兼容现有代码
  col: number;                   // 兼容现有代码 (等同于column)
}

export interface GridDimensions {
  rows: number;
  cols: number;
  cellSize: number;
  gap: number;
}

export interface GridConfig {
  fontSize: number;
  matrixMargin: number;
  gridColor: string;
  cellShape: 'rounded' | 'circle' | 'square';
  displayMode: 'hidden' | 'visible';
} 