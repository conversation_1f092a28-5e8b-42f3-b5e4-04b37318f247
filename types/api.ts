/**
 * API类型定义 - 全栈架构
 * 🎯 核心价值：统一前后端数据接口，确保类型安全
 * 🔄 RESTful设计：标准化API响应格式
 * ⚡ TypeScript支持：完整的类型推导和验证
 */

// === 通用API响应格式 ===
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// === 用户相关API ===
export interface CreateUserRequest {
  email?: string;
  name?: string;
}

export interface UserResponse {
  id: string;
  email?: string;
  name?: string;
  createdAt: string;
  updatedAt: string;
}

// === 项目相关API ===
export interface CreateProjectRequest {
  name: string;
  description?: string;
}

export interface UpdateProjectRequest {
  name?: string;
  description?: string;
}

export interface ProjectResponse {
  id: string;
  name: string;
  description?: string;
  userId: string;
  createdAt: string;
  updatedAt: string;
}

// === 颜色数据API ===
export interface ColorDataRequest {
  colorType: string;
  level: number;
  coordinates: [number, number][];
  colorValue: string;
  visible?: boolean;
}

export interface ColorDataResponse {
  id: string;
  projectId: string;
  colorType: string;
  level: number;
  coordinates: [number, number][];
  colorValue: string;
  visible: boolean;
  createdAt: string;
  updatedAt: string;
}

// === 网格数据API ===
export interface GridDataRequest {
  row: number;
  col: number;
  x: number;
  y: number;
  number: number;
  color: string;
  level: number;
  groupId?: number;
}

export interface GridDataResponse {
  id: string;
  projectId: string;
  row: number;
  col: number;
  x: number;
  y: number;
  number: number;
  color: string;
  level: number;
  groupId?: number;
  createdAt: string;
  updatedAt: string;
}

// === 黑色格子数据API ===
export interface BlackCellDataRequest {
  x: number;
  y: number;
  letter: string;
  visible?: boolean;
}

export interface BlackCellDataResponse {
  id: string;
  projectId: string;
  x: number;
  y: number;
  letter: string;
  visible: boolean;
  createdAt: string;
  updatedAt: string;
}

// === 版本管理API ===
export interface CreateVersionRequest {
  name: string;
  description?: string;
  versionType: 'default' | 'group' | 'mixed' | 'matrix';
  data: any; // 完整的状态快照
}

export interface VersionResponse {
  id: string;
  projectId: string;
  name: string;
  description?: string;
  versionType: string;
  data: any;
  isCurrent: boolean;
  createdAt: string;
  updatedAt: string;
}

// === 项目设置API ===
export interface ProjectSettingsRequest {
  // 样式设置
  currentTheme?: 'light' | 'dark';
  showBlackCells?: boolean;
  matrixStyles?: any;
  colorScheme?: any;
  
  // 动态样式设置
  fontSize?: number;
  matrixMargin?: number;
  cellShape?: 'square' | 'rounded' | 'circle';
  displayMode?: 'number' | 'coordinate' | 'hidden';
  enableCircleScale?: boolean;
  circleScaleFactor?: number;
  enableVirtualization?: boolean;
  
  // 全局显示控制
  showAllNumbers?: boolean;
  showAllColors?: boolean;
  showAllLevel1?: boolean;
  showAllLevel2?: boolean;
  showAllLevel3?: boolean;
  showAllLevel4?: boolean;
}

export interface ProjectSettingsResponse {
  id: string;
  projectId: string;
  currentTheme: string;
  showBlackCells: boolean;
  matrixStyles: any;
  colorScheme: any;
  fontSize: number;
  matrixMargin: number;
  cellShape: string;
  displayMode: string;
  enableCircleScale: boolean;
  circleScaleFactor: number;
  enableVirtualization: boolean;
  showAllNumbers: boolean;
  showAllColors: boolean;
  showAllLevel1: boolean;
  showAllLevel2: boolean;
  showAllLevel3: boolean;
  showAllLevel4: boolean;
  createdAt: string;
  updatedAt: string;
}

// === 组合数据API ===
export interface CombinationDataRequest {
  currentMode?: string;
  showAllColors?: boolean;
  showAllLevels?: boolean;
  selectedGroups?: any;
  swapGroupsData?: any;
  modeActivation?: any;
  operationHistory?: any;
  shortcutEnabled?: boolean;
}

export interface CombinationDataResponse {
  id: string;
  projectId: string;
  currentMode: string;
  showAllColors: boolean;
  showAllLevels: boolean;
  selectedGroups: any;
  swapGroupsData: any;
  modeActivation: any;
  operationHistory: any;
  shortcutEnabled: boolean;
  createdAt: string;
  updatedAt: string;
}

// === 数据迁移API ===
export interface MigrationRequest {
  userId: string;
}

export interface MigrationResponse {
  isCompleted: boolean;
  lastMigrationDate?: string;
  migratedStores: string[];
  errors: string[];
}

// === 批量操作API ===
export interface BatchColorDataRequest {
  operations: {
    action: 'create' | 'update' | 'delete';
    data: ColorDataRequest;
    id?: string;
  }[];
}

export interface BatchGridDataRequest {
  operations: {
    action: 'create' | 'update' | 'delete';
    data: GridDataRequest;
    id?: string;
  }[];
}

// === 查询参数 ===
export interface ColorDataQuery {
  colorType?: string;
  level?: number;
  visible?: boolean;
}

export interface GridDataQuery {
  minX?: number;
  maxX?: number;
  minY?: number;
  maxY?: number;
  color?: string;
  level?: number;
}

export interface VersionQuery {
  versionType?: string;
  isCurrent?: boolean;
}
