/**
 * 测试按钮函数修复
 * 验证所有按钮样式函数是否正确导入和工作
 */

// 模拟导入（在实际项目中这些会从正确的路径导入）
const buttonUtils = require('./utils/buttonUtils.ts');

console.log('🧪 测试按钮工具函数...\n');

// 测试基础按钮样式
console.log('1. 测试 getButtonStyle:');
try {
  const activeStyle = buttonUtils.getButtonStyle('active');
  const inactiveStyle = buttonUtils.getButtonStyle('inactive');
  console.log('✅ getButtonStyle 工作正常');
  console.log('   - active:', activeStyle);
  console.log('   - inactive:', inactiveStyle);
} catch (error) {
  console.log('❌ getButtonStyle 错误:', error.message);
}

// 测试Tab样式
console.log('\n2. 测试 getTabStyle:');
try {
  const activeTab = buttonUtils.getTabStyle(true);
  const inactiveTab = buttonUtils.getTabStyle(false);
  console.log('✅ getTabStyle 工作正常');
  console.log('   - active:', activeTab);
  console.log('   - inactive:', inactiveTab);
} catch (error) {
  console.log('❌ getTabStyle 错误:', error.message);
}

// 测试网格样式
console.log('\n3. 测试 getGridStyle:');
try {
  const grid2 = buttonUtils.getGridStyle(2);
  const grid3 = buttonUtils.getGridStyle(3);
  const grid4 = buttonUtils.getGridStyle(4);
  const grid5 = buttonUtils.getGridStyle(5);
  console.log('✅ getGridStyle 工作正常');
  console.log('   - 2列:', grid2);
  console.log('   - 3列:', grid3);
  console.log('   - 4列:', grid4);
  console.log('   - 5列:', grid5);
} catch (error) {
  console.log('❌ getGridStyle 错误:', error.message);
}

// 测试高级按钮样式
console.log('\n4. 测试 getAdvancedButtonStyle:');
try {
  const primaryMd = buttonUtils.getAdvancedButtonStyle('primary', 'md');
  const secondaryLg = buttonUtils.getAdvancedButtonStyle('secondary', 'lg', 'custom-class');
  console.log('✅ getAdvancedButtonStyle 工作正常');
  console.log('   - primary md:', primaryMd);
  console.log('   - secondary lg + custom:', secondaryLg);
} catch (error) {
  console.log('❌ getAdvancedButtonStyle 错误:', error.message);
}

// 测试激活状态按钮
console.log('\n5. 测试 getActiveButtonStyle:');
try {
  const activeBtn = buttonUtils.getActiveButtonStyle(true, 'sm');
  const inactiveBtn = buttonUtils.getActiveButtonStyle(false, 'lg');
  console.log('✅ getActiveButtonStyle 工作正常');
  console.log('   - active sm:', activeBtn);
  console.log('   - inactive lg:', inactiveBtn);
} catch (error) {
  console.log('❌ getActiveButtonStyle 错误:', error.message);
}

console.log('\n🎉 所有按钮函数测试完成！');
console.log('✅ ReferenceError: getButtonStyle is not defined 已修复');
